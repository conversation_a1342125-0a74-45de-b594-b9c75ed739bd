<mxfile host="app.diagrams.net" modified="2025-06-20T00:00:00.000Z" agent="Claude Code" etag="PBCS011-Flowchart" version="24.2.5" type="device" pages="7">
  <diagram id="main-flow" name="Main Program Flow">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start" value="START&#xa;PBCS011" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="20" width="100" height="60" as="geometry" />
        </mxCell>
        <mxCell id="init" value="000-INITIAL&#xa;Initialize Program&#xa;Validate Input" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="validate-tran" value="Validate TRAN-ID&#xa;('0001' or '0002')" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="314" y="220" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="error1" value="Set Error Status&#xa;Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="584" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="validate-status" value="Validate STATUS-CHQ&#xa;('1' to '5')" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="314" y="340" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="process-main" value="2000-PROCESS-RTN&#xa;Main Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="460" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="check-tran-type" value="TRAN-ID = ?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="334" y="560" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="summary" value="2500-SUMMARY-RTN&#xa;Summary Processing" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="154" y="680" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="detail-status" value="STATUS-CHQ = ?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="514" y="680" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="setup" value="2600-CHQSETUP-RTN&#xa;Setup Transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="374" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="paid" value="2700-CHQPAID-RTN&#xa;Paid Transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="554" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="return" value="2800-CHQRETURN-RTN&#xa;Return Transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="734" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="all-trn" value="2900-CHQALLTRN-RTN&#xa;All Transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="464" y="900" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="all-dwn" value="2950-CHQALLDWN-RTN&#xa;All Download Trans" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="644" y="900" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="terminate" value="9000-TERMINATE-RTN&#xa;Close Cursors&#xa;Return Results" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="334" y="1020" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end" value="END" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="364" y="1120" width="100" height="60" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="c1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start" target="init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="init" target="validate-tran">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validate-tran" target="error1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c3-label" value="Invalid" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validate-tran" target="validate-status">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c4-label" value="Valid" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validate-status" target="error1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="554" y="380" />
              <mxPoint x="554" y="310" />
              <mxPoint x="644" y="310" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c5-label" value="Invalid" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c5">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="validate-status" target="process-main">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c6-label" value="Valid" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c6">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="process-main" target="check-tran-type">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="check-tran-type" target="summary">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c8-label" value="0001" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c8">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="check-tran-type" target="detail-status">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c9-label" value="0002" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c9">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="detail-status" target="setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c10-label" value="1" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c10">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="detail-status" target="paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c11-label" value="2" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c11">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="detail-status" target="return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c12-label" value="3" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c12">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="detail-status" target="all-trn">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c13-label" value="4" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c13">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="detail-status" target="all-dwn">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="c14-label" value="5" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="c14">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="c15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="summary" target="terminate">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="234" y="1000" />
              <mxPoint x="414" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="setup" target="terminate">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="454" y="1000" />
              <mxPoint x="414" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="paid" target="terminate">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="634" y="1000" />
              <mxPoint x="414" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="return" target="terminate">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="814" y="1000" />
              <mxPoint x="414" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="all-trn" target="terminate">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="544" y="1000" />
              <mxPoint x="414" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="all-dwn" target="terminate">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="724" y="1000" />
              <mxPoint x="414" y="1000" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="c21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="terminate" target="end">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="summary-flow" name="Summary Processing (2500)">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start-summary" value="2500-SUMMARY-RTN&#xa;START" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="20" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-cursor" value="OPEN CURSOR&#xa;SUMMARY" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="344" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sql-check1" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="344" y="220" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sql-error1" value="Handle SQL Error&#xa;Close Cursor&#xa;Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="574" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-loop" value="FETCH CURSOR&#xa;SUMMARY" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="344" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-check" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="344" y="440" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="build-summary" value="Build Summary Record&#xa;- Branch Code&#xa;- Setup Count&#xa;- Paid Count&#xa;- Return Count" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="324" y="560" width="200" height="80" as="geometry" />
        </mxCell>
        <mxCell id="write-record" value="Write Summary Record&#xa;to BCBCMDTL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="344" y="680" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="increment" value="Increment&#xa;Record Counter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="344" y="780" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="close-cursor" value="CLOSE CURSOR&#xa;SUMMARY" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="344" y="880" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end-summary" value="RETURN TO&#xa;MAIN PROGRAM" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="364" y="980" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="s1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start-summary" target="open-cursor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="open-cursor" target="sql-check1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check1" target="sql-error1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s3-label" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="s3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="s4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check1" target="fetch-loop">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s4-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="s4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="s5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-loop" target="fetch-check">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check" target="close-cursor">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="194" y="480" />
              <mxPoint x="194" y="910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="s6-label" value="No (End of Data)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="s6">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="s7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check" target="build-summary">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s7-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="s7">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="s8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="build-summary" target="write-record">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="write-record" target="increment">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="s10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="increment" target="fetch-loop">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="574" y="810" />
              <mxPoint x="574" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="s11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="close-cursor" target="end-summary">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="setup-flow" name="Setup Processing (2600)">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start-setup" value="2600-CHQSETUP-RTN&#xa;START" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="20" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-setup" value="OPEN CURSOR&#xa;DETSETUP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sql-check-setup" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="220" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sql-error-setup" value="Handle SQL Error&#xa;Close Cursor&#xa;Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="584" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-setup" value="FETCH CURSOR&#xa;DETSETUP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-check-setup" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="440" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="build-detail-setup" value="Build Detail Record&#xa;- Cheque Number&#xa;- Amount&#xa;- Setup Date&#xa;- Branch Code&#xa;- Status (SETUP)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="324" y="560" width="220" height="100" as="geometry" />
        </mxCell>
        <mxCell id="write-setup" value="Write Detail Record&#xa;to BCBCMDTL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="700" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="increment-setup" value="Increment&#xa;Record Counter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="354" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="close-setup" value="CLOSE CURSOR&#xa;DETSETUP" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="900" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end-setup" value="RETURN TO&#xa;MAIN PROGRAM" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="374" y="1000" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="st1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start-setup" target="open-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="open-setup" target="sql-check-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-setup" target="sql-error-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st3-label" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="st3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="st4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-setup" target="fetch-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st4-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="st4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="st5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-setup" target="fetch-check-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-setup" target="close-setup">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="194" y="480" />
              <mxPoint x="194" y="930" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="st6-label" value="No (End)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="st6">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="st7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-setup" target="build-detail-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st7-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="st7">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="st8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="build-detail-setup" target="write-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="write-setup" target="increment-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="st10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="increment-setup" target="fetch-setup">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="584" y="830" />
              <mxPoint x="584" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="st11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="close-setup" target="end-setup">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="paid-flow" name="Paid Processing (2700)">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start-paid" value="2700-CHQPAID-RTN&#xa;START" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="20" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-paid" value="OPEN CURSOR&#xa;DETPAID" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sql-check-paid" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="220" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sql-error-paid" value="Handle SQL Error&#xa;Close Cursor&#xa;Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="584" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-paid" value="FETCH CURSOR&#xa;DETPAID" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-check-paid" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="440" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="build-detail-paid" value="Build Detail Record&#xa;- Cheque Number&#xa;- Amount&#xa;- Paid Date&#xa;- Branch Code&#xa;- Status (PAID/CREP)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="324" y="560" width="220" height="100" as="geometry" />
        </mxCell>
        <mxCell id="write-paid" value="Write Detail Record&#xa;to BCBCMDTL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="700" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="increment-paid" value="Increment&#xa;Record Counter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="354" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="close-paid" value="CLOSE CURSOR&#xa;DETPAID" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="900" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end-paid" value="RETURN TO&#xa;MAIN PROGRAM" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="374" y="1000" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="pd1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start-paid" target="open-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="open-paid" target="sql-check-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-paid" target="sql-error-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd3-label" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="pd3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pd4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-paid" target="fetch-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd4-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="pd4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pd5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-paid" target="fetch-check-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-paid" target="close-paid">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="194" y="480" />
              <mxPoint x="194" y="930" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="pd6-label" value="No (End)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="pd6">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pd7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-paid" target="build-detail-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd7-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="pd7">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="pd8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="build-detail-paid" target="write-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="write-paid" target="increment-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="pd10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="increment-paid" target="fetch-paid">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="584" y="830" />
              <mxPoint x="584" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="pd11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="close-paid" target="end-paid">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="return-flow" name="Return Processing (2800)">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start-return" value="2800-CHQRETURN-RTN&#xa;START" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="20" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-return" value="OPEN CURSOR&#xa;DETRETURN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sql-check-return" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="220" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sql-error-return" value="Handle SQL Error&#xa;Close Cursor&#xa;Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="584" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-return" value="FETCH CURSOR&#xa;DETRETURN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-check-return" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="440" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="build-detail-return" value="Build Detail Record&#xa;- Cheque Number&#xa;- Amount&#xa;- Return Date&#xa;- Branch Code&#xa;- Status (RREP/RETC)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="324" y="560" width="220" height="100" as="geometry" />
        </mxCell>
        <mxCell id="write-return" value="Write Detail Record&#xa;to BCBCMDTL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="700" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="increment-return" value="Increment&#xa;Record Counter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="354" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="close-return" value="CLOSE CURSOR&#xa;DETRETURN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="900" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end-return" value="RETURN TO&#xa;MAIN PROGRAM" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="374" y="1000" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="rt1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start-return" target="open-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="open-return" target="sql-check-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-return" target="sql-error-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt3-label" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="rt3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rt4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-return" target="fetch-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt4-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="rt4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rt5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-return" target="fetch-check-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-return" target="close-return">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="194" y="480" />
              <mxPoint x="194" y="930" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rt6-label" value="No (End)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="rt6">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rt7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-return" target="build-detail-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt7-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="rt7">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rt8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="build-detail-return" target="write-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="write-return" target="increment-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rt10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="increment-return" target="fetch-return">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="584" y="830" />
              <mxPoint x="584" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rt11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="close-return" target="end-return">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="all-trn-flow" name="All Transactions (2900)">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="start-all" value="2900-CHQALLTRN-RTN&#xa;START" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="364" y="20" width="140" height="60" as="geometry" />
        </mxCell>
        <mxCell id="open-all" value="OPEN CURSOR&#xa;DETALLTRN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="120" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="sql-check-all" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="220" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="sql-error-all" value="Handle SQL Error&#xa;Close Cursor&#xa;Return Error" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="584" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-all" value="FETCH CURSOR&#xa;DETALLTRN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="340" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fetch-check-all" value="SQLCODE = 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="354" y="440" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="build-detail-all" value="Build Detail Record&#xa;- All Transaction Types&#xa;- Complete Details&#xa;- All Status Types&#xa;- Branch Information" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="324" y="560" width="220" height="100" as="geometry" />
        </mxCell>
        <mxCell id="write-all" value="Write Detail Record&#xa;to BCBCMDTL" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="700" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="increment-all" value="Increment&#xa;Record Counter" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="354" y="800" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="close-all" value="CLOSE CURSOR&#xa;DETALLTRN" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="354" y="900" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="end-all" value="RETURN TO&#xa;MAIN PROGRAM" style="ellipse;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="374" y="1000" width="120" height="60" as="geometry" />
        </mxCell>
        <!-- Connections -->
        <mxCell id="al1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="start-all" target="open-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="open-all" target="sql-check-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-all" target="sql-error-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al3-label" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="al3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="al4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="sql-check-all" target="fetch-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al4-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="al4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="al5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-all" target="fetch-check-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-all" target="close-all">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="194" y="480" />
              <mxPoint x="194" y="930" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="al6-label" value="No (End)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="al6">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="al7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fetch-check-all" target="build-detail-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al7-label" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="al7">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="al8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="build-detail-all" target="write-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="write-all" target="increment-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="al10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="increment-all" target="fetch-all">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="584" y="830" />
              <mxPoint x="584" y="370" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="al11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="close-all" target="end-all">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="database-schema" name="Database Schema">
    <mxGraphModel dx="1422" dy="714" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="title-db" value="PBCS011 Database Schema&#xa;and Table Relationships" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=16;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="264" y="20" width="300" height="60" as="geometry" />
        </mxCell>
        <!-- Main Table -->
        <mxCell id="main-table" value="HOST_CHEQUE_DETAIL&#xa;(Primary Transaction Table)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="314" y="120" width="200" height="60" as="geometry" />
        </mxCell>
        <mxCell id="main-fields" value="Key Fields:&#xa;• CHEQUE_NO (Primary Key)&#xa;• AMOUNT&#xa;• SETUP_DATE&#xa;• PAID_DATE&#xa;• RETURN_DATE&#xa;• STATUS_CODE&#xa;• BRANCH_CODE&#xa;• CLEARING_ZONE" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="314" y="200" width="200" height="120" as="geometry" />
        </mxCell>
        <!-- Supporting Tables -->
        <mxCell id="custom-support" value="HOST_CUSTOM_SUPPOR&#xa;(Customer Support)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="74" y="380" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="custom-fields" value="Fields:&#xa;• ACCOUNT_NO&#xa;• CUSTOMER_INFO&#xa;• SUPPORT_FLAG" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="74" y="440" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="account-profile" value="HOST_ACCOUNT_PROFILE&#xa;(Account Information)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="264" y="380" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="account-fields" value="Fields:&#xa;• ACCOUNT_NO&#xa;• ACCOUNT_TYPE&#xa;• BRANCH_CODE&#xa;• STATUS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="264" y="440" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="group-profile" value="HOST_GROUP_PROFILE&#xa;(Group Information)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="454" y="380" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="group-fields" value="Fields:&#xa;• GROUP_CODE&#xa;• GROUP_NAME&#xa;• GROUP_TYPE&#xa;• STATUS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="454" y="440" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="branch-zone" value="HOST_BKBR_ZONE&#xa;(Branch Zone Mapping)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="644" y="380" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="branch-fields" value="Fields:&#xa;• BRANCH_CODE&#xa;• ZONE_CODE&#xa;• ZONE_NAME&#xa;• REGION" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="644" y="440" width="160" height="60" as="geometry" />
        </mxCell>
        <!-- SQL Cursors -->
        <mxCell id="cursors-title" value="SQL Cursors Used in Program" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="314" y="550" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="cursor1" value="SUMMARY&#xa;Aggregate counts by branch" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="94" y="620" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cursor2" value="DETSETUP&#xa;Setup transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="254" y="620" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cursor3" value="DETPAID&#xa;Paid transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="414" y="620" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cursor4" value="DETRETURN&#xa;Return transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="574" y="620" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cursor5" value="DETALLTRN&#xa;All transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="174" y="690" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="cursor6" value="DETALLDWN&#xa;All download transactions" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="334" y="690" width="140" height="50" as="geometry" />
        </mxCell>
        <!-- Output -->
        <mxCell id="output-title" value="Output Destination" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="314" y="780" width="200" height="40" as="geometry" />
        </mxCell>
        <mxCell id="output-file" value="CICS File: BCBCMDTL&#xa;(BC Command Detail File)&#xa;Record Length: 600 bytes" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="314" y="840" width="200" height="60" as="geometry" />
        </mxCell>
        <!-- Relationships -->
        <mxCell id="rel1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;startArrow=none;startFill=0;endArrow=classic;endFill=1;" edge="1" parent="1" source="main-table" target="custom-support">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="314" y="150" />
              <mxPoint x="154" y="150" />
              <mxPoint x="154" y="405" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rel1-label" value="LEFT JOIN" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="rel1">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rel2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;startArrow=none;startFill=0;endArrow=classic;endFill=1;" edge="1" parent="1" source="main-table" target="account-profile">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="344" y="180" />
              <mxPoint x="344" y="405" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rel2-label" value="LEFT JOIN" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="rel2">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rel3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;startArrow=none;startFill=0;endArrow=classic;endFill=1;" edge="1" parent="1" source="main-table" target="group-profile">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="484" y="180" />
              <mxPoint x="534" y="180" />
              <mxPoint x="534" y="405" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rel3-label" value="LEFT JOIN" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="rel3">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rel4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#666666;strokeWidth=2;startArrow=none;startFill=0;endArrow=classic;endFill=1;" edge="1" parent="1" source="main-table" target="branch-zone">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="514" y="150" />
              <mxPoint x="724" y="150" />
              <mxPoint x="724" y="405" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rel4-label" value="LEFT JOIN" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=9;" vertex="1" connectable="0" parent="rel4">
          <mxGeometry x="-0.1" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>