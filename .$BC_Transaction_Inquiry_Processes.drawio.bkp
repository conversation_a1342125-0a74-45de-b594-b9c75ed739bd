<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2024-06-20T00:00:00.000Z" agent="draw.io" etag="xxx" version="21.0.0" type="device">
  <diagram name="BC Transaction Inquiry Processes" id="bc-inquiry-processes">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1654" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Title -->
        <mxCell id="title" value="BC Transaction Inquiry System - Two Main Processes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=18;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="527" y="20" width="600" height="30" as="geometry" />
        </mxCell>
        
        <!-- BCMSMAIN Entry Point -->
        <mxCell id="bcmsmain" value="BCMSMAIN&#xa;(Entry Point)" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="727" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        
        <!-- Decision Diamond -->
        <mxCell id="decision" value="Transaction&#xa;Type?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="747" y="180" width="80" height="80" as="geometry" />
        </mxCell>
        
        <!-- BCBCS060 Process (Left Side) -->
        <mxCell id="bcbcs060-start" value="BCBCS060&#xa;Summary Inquiry" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="400" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs060-init" value="100-INITIAL-RTN&#xa;• Setup CICS handling&#xa;• Initialize variables&#xa;• Set reply queue info" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="420" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs060-main" value="200-MAINLINE&#xa;• Prepare account data&#xa;• Set transaction dates&#xa;• LINK to PBCS011" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="520" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs060-format" value="1000-FORMAT-OUTPUT&#xa;• Read BCBCMDTL file&#xa;• Skip unwanted records&#xa;• Format summary data&#xa;• Count by branch" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="620" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs060-output" value="2000-FORMAT-FILE-OUT&#xa;• Setup counts&#xa;• Paid counts&#xa;• Return counts&#xa;• Total calculations" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="730" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs060-exit" value="300-PGM-EXIT&#xa;• Format final output&#xa;• Return to caller" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="400" y="830" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- BCBCS061 Process (Right Side) -->
        <mxCell id="bcbcs061-start" value="BCBCS061&#xa;Detail Inquiry" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="1080" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-init" value="100-INITIAL-RTN&#xa;• Setup CICS handling&#xa;• Initialize variables&#xa;• Set reply queue info" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1060" y="420" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-decision" value="Last Seq Num&#xa;= 0?" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1100" y="520" width="80" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-main" value="200-MAINLINE&#xa;• Prepare account data&#xa;• Set branch/status&#xa;• LINK to PBCS011&#xa;• First time fetch" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="920" y="620" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-next" value="4000-NEXT-FETCH&#xa;• Calculate next serial&#xa;• Continue from last seq&#xa;• Read next 50 records&#xa;• Pagination support" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1240" y="620" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-format" value="1000-FORMAT-OUTPUT&#xa;• Read BCBCMDTL file&#xa;• Skip to last sequence&#xa;• Read next 50 records&#xa;• Format detail data" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1060" y="730" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-detail" value="2000-FORMAT-FILE-OUT&#xa;• Individual cheque details&#xa;• 573-byte detail record&#xa;• Sequence numbering" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1060" y="840" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-next-prep" value="5000-FETCH-FOR-NEXT-REQ&#xa;• Pre-fetch next record&#xa;• Set continuation flag&#xa;• Prepare for next request" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1240" y="840" width="160" height="70" as="geometry" />
        </mxCell>
        
        <mxCell id="bcbcs061-exit" value="300-PGM-EXIT&#xa;• Format final output&#xa;• Return to caller" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1080" y="940" width="120" height="50" as="geometry" />
        </mxCell>
        
        <!-- PBCS011 Common Module (Center) -->
        <mxCell id="pbcs011" value="PBCS011&#xa;Common Processing Module" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="727" y="320" width="120" height="60" as="geometry" />
        </mxCell>
        
        <mxCell id="pbcs011-sql" value="SQL Processing&#xa;• SUMMARY cursor&#xa;• DETPAID cursor&#xa;• DETRETURN cursor&#xa;• DETSETUP cursor&#xa;• DETALLTRN cursor" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="707" y="420" width="160" height="90" as="geometry" />
        </mxCell>
        
        <mxCell id="pbcs011-tables" value="Database Tables&#xa;• HOST_CHEQUE_DETAIL&#xa;• HOST_CUSTOM_SUPPOR&#xa;• HOST_ACCOUNT_PROFILE&#xa;• HOST_GROUP_PROFILE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="707" y="540" width="160" height="80" as="geometry" />
        </mxCell>
        
        <mxCell id="pbcs011-file" value="BCBCMDTL File&#xa;• Write processed data&#xa;• 600-byte records&#xa;• Summary/Detail format" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="707" y="650" width="160" height="70" as="geometry" />
        </mxCell>
        
        <!-- Arrows and Connections -->
        <!-- Main flow -->
        <mxCell id="arrow1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcmsmain" target="decision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Summary path -->
        <mxCell id="arrow2" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision" target="bcbcs060-start">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label1" value="Summary" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="580" y="200" width="60" height="20" as="geometry" />
        </mxCell>
        
        <!-- Detail path -->
        <mxCell id="arrow3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="decision" target="bcbcs061-start">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label2" value="Detail" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="940" y="200" width="60" height="20" as="geometry" />
        </mxCell>
        
        <!-- BCBCS060 flow -->
        <mxCell id="arrow4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs060-start" target="bcbcs060-init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs060-init" target="bcbcs060-main">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs060-main" target="bcbcs060-format">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs060-format" target="bcbcs060-output">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs060-output" target="bcbcs060-exit">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- BCBCS061 flow -->
        <mxCell id="arrow9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-start" target="bcbcs061-init">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-init" target="bcbcs061-decision">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-decision" target="bcbcs061-main">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label3" value="Yes" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1020" y="540" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="arrow12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-decision" target="bcbcs061-next">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="label4" value="No" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1200" y="540" width="30" height="20" as="geometry" />
        </mxCell>
        
        <!-- Links to PBCS011 -->
        <mxCell id="arrow13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6666;strokeWidth=2;" edge="1" parent="1" source="bcbcs060-main" target="pbcs011">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6666;strokeWidth=2;" edge="1" parent="1" source="bcbcs061-main" target="pbcs011">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#ff6666;strokeWidth=2;" edge="1" parent="1" source="bcbcs061-next" target="pbcs011">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- PBCS011 internal flow -->
        <mxCell id="arrow16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="pbcs011" target="pbcs011-sql">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="pbcs011-sql" target="pbcs011-tables">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="pbcs011-tables" target="pbcs011-file">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <!-- Continue BCBCS061 flow -->
        <mxCell id="arrow19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-main" target="bcbcs061-format">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1000" y="760" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-next" target="bcbcs061-format">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1320" y="760" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="arrow21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-format" target="bcbcs061-detail">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-detail" target="bcbcs061-exit">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="arrow23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="bcbcs061-format" target="bcbcs061-next-prep">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1220" y="770" />
              <mxPoint x="1220" y="875" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="label5" value="If 50 records" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=9;" vertex="1" parent="1">
          <mxGeometry x="1220" y="800" width="70" height="20" as="geometry" />
        </mxCell>
        
        <!-- Legend -->
        <mxCell id="legend-title" value="Legend:" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=12;fontStyle=1;" vertex="1" parent="1">
          <mxGeometry x="50" y="1020" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend1" value="CICS LINK calls" style="text;html=1;strokeColor=#ff6666;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="1040" width="100" height="20" as="geometry" />
        </mxCell>
        <mxCell id="legend-line1" value="" style="endArrow=classic;html=1;rounded=0;strokeColor=#ff6666;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="150" y="1050" as="sourcePoint" />
            <mxPoint x="200" y="1050" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
