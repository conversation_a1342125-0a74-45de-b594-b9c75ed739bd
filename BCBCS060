 CBL XOPTS(COBOL2)                                                      00010000
 CBL MAP,OBJ,RENT,RES,DYNAM,OPT,LIB,DATA(31),LIST,APOST,TRUNC(BIN)      00020000
      ***************************************************************** 00030000
       IDENTIFICATION DIVISION.                                         00040000
      ***************************************************************** 00050000
       PROGRAM-ID.                      BCBCS060 .                      00060025
       DATE-COMPILED.                                                   00070000
      *REMARKS.                                                         00080000
      ***************************************************************** 00090000
      * DESCRIPTION :                                                 * 00100000
      *    PROGRAM BC SUMMARY TRANSACTION INQUIRY                     * 00280012
      * FORMAT :                                                      * 00290000
      *    LINK VIA COMMAREA FROM BCMSMAIN                            * 00300000
      * AUTHOR :                                                      * 00310000
      *    TRIRAT   SUWANPRATEEB / IBM THAILAND                       * 00320000
      *                                                               * 01150000
      * MODIFY :                                                      * 01151088
      *    25/03/04 : EXTEND FILE SIZE FROM 128 TO 300 BY             * 01152089
      *               ADD FILLER FOM 72 TO 249                        * 01153089
      *    20/12/05 : EXTEND FILE SIZE FROM 300 TO 600 BY             * 01154092
      *               ADD FILLER FOM 249 TO 549                       * 01155092
      * MODIFIED :                                                    * 01156093
      * 07/08/06 :  UR49030004 -- BRANCH1K PROJECT.                   * 01157093
      *             MODIFIED BY RAPEEPHAN S.                          * 01158093
      *                                                               * 01159093
      ***************************************************************** 01160000
      *                                                                 01170000
      *                                                                 01180000
      ***************************************************************** 01190000
       ENVIRONMENT DIVISION.                                            01200000
      ***************************************************************** 01210000
      *                                                                 01220000
      *                                                                 01230000
      ***************************************************************** 01240000
       DATA DIVISION.                                                   01250000
      ***************************************************************** 01260000
      *                                                                 01270000
      *===============================================================* 01280000
       WORKING-STORAGE SECTION.                                         01290000
      *===============================================================* 01300000
      *                                                                 01310000
       01  W00-EYECATCHER          PIC X(32)   VALUE                    01320000
                 '* ***** WORKING STORAGE ***** *'.                     01330000
      *                                                                 01340000
       01  W01-PROGID               PIC X(8) VALUE 'BCBCS060'.          01350025
      *                                                                 01370000
       01  W02-RIDFLD.                                                  01390039
           05 W02-TRANSID           PIC X(04) VALUE SPACES.             01400046
           05 W02-ACCT-NO           PIC X(10) VALUE SPACES.             01410046
           05 W02-TRANS-DATE        PIC X(08) VALUE SPACES.             01420046
           05 W02-SERIAL-NO         PIC X(05) VALUE SPACES.             01430046
       01  TEMP-CHECK               PIC X(14) VALUE SPACES.             01440047
       01  TEMP-NUM                 PIC 9(07) VALUE ZEROS.              01450058
      *                                                                 01510000
      * W03 holds the values for CICS commands.                         01520000
      *                                                                 01530000
       01  W03-RESP1                PIC S9(7) COMP.                     01550000
       01  W03-RESP2                PIC S9(7) COMP.                     01551000
       01  W03-LENGTHRETD           PIC S9(9) COMP.                     01560000
       01  W03-LENGTHSENT           PIC S9(9) COMP.                     01570000
       01  W03-SPACE                PIC X VALUE SPACE.                  01580000
      *                                                                 01590000
       01  BC-PROGRAM               PIC X(8) VALUE 'PBCS011 '.          01611027
       01  BC-FILENAME              PIC X(8) VALUE 'BCBCMDTL'.          01612026
       01  W04-PROBLEM-FLAG         PIC 9.                              01620000
       01  W04-NO-PROBLEM           PIC 9 VALUE 0.                      01630000
       01  W04-PROBLEM              PIC 9 VALUE 1.                      01640000
       01  CNTTEL                   PIC 9(4) COMP VALUE 0.              01640394
       01  STOP-FLAG                PIC X VALUE 'N'.                    01641000
       01  TOTAL-ACCT               PIC 9(6) VALUE ZEROS.               01641100
       01  CNT-LOOP                 PIC 9(6) VALUE ZEROS.               01641200
       01  CHARCOUNT                PIC 9999 VALUE ZEROS.               01641300
       01  ENDCOUNT                 PIC 9999 VALUE ZEROS.               01642000
       01  W04-CALL-ERROR-MSG.                                          01643000
           05 W04-CALL              PIC X(20) VALUE SPACE.              01643100
           05                       PIC X(11) VALUE                     01644000
                     'RESP  CODE:'.                                     01645000
           05 W04-RESP              PIC Z(08)9.                         01646000
           05                       PIC X(11) VALUE                     01647000
                     'RESP2 CODE:'.                                     01648000
           05 W04-RESP2             PIC Z(08)9.                         01649000
      *                                                                 01650000
       01  W05-TS-MESSAGE-LENGTH    PIC S9(4) BINARY.                   01651000
       01  W05-ABSTIME              PIC S9(15) COMP-3.                  01652000
       01  W05-DD                   PIC S9(8)  BINARY VALUE ZERO.       01653000
       01  W05-MM                   PIC S9(8)  BINARY VALUE ZERO.       01654000
       01  W05-YYYY                 PIC S9(8)  BINARY VALUE ZERO.       01655000
       01  W05-DATE                 PIC X(11)  VALUE  LOW-VALUES.       01656000
       01  W05-DATEFORM             PIC X(6)   VALUE  LOW-VALUES.       01657000
       01  W05-YYMMDD               PIC X(8)   VALUE  SPACES.           01658000
       01  W05-TIME                 PIC X(8)   VALUE  SPACES.           01659000
      *                                                                 01770000
       01  BC-LINK.                                                     01770125
           05 BC-IN.                                                    01770225
            10 BCI-TRANS-ID              PIC X(04) VALUE '0001'.        01770325
            10 BCI-ACCT-NO               PIC X(10) VALUE SPACES.        01770425
      * for branch1k                                                    01770594
      *     10 BCI-SETUP-BRANCH          PIC X(03) VALUE SPACES.        01770694
            10 BCI-SETUP-BRANCH          PIC X(04) VALUE SPACES.        01770794
      * end branch1k                                                    01770894
            10 BCI-STATUS                PIC 9(01) VALUE 1.             01770994
            10 BCI-TRANS-FROM-DATE       PIC X(10) VALUE SPACES.        01771094
            10 BCI-TRANS-TO-DATE         PIC X(10) VALUE SPACES.        01771194
            10 BCI-TRANS-DATE            PIC X(10) VALUE SPACES.        01772094
      * for branch1k                                                    01773094
      *     10 FILLER                    PIC X(52) VALUE SPACES.        01774494
            10 FILLER                    PIC X(51) VALUE SPACES.        01774594
      * end branch1k                                                    01774694
           05 BC-OUT REDEFINES BC-IN.                                   01776770
            10 BCO-APPLICATION-NAME      PIC X(03).                     01776825
            10 BCO-STATUS-FLAG           PIC X(02).                     01776933
            10 BCO-STATUS-NUMBER         PIC X(05).                     01777025
            10 BCO-STATUS-DESCRIPTION    PIC X(50).                     01777125
            10 BCO-SQLSTATE              PIC X(05).                     01777225
            10 BCO-SQLCODE               PIC X(10).                     01777325
            10 FILLER                    PIC X(25).                     01780212
      *                                                                 01780300
       01 W06-OUTPUT.                                                   01780400
           05 W06-REC-TYPE               PIC X(01) VALUE 'H'.           01780500
           05 W06-TRAN-CODE              PIC X(06) VALUE 'BCS060'.      01780625
           05 W06-RES-CODE               PIC 9(04) VALUE ZEROS.         01780700
           05 W06-RES-TYPE               PIC 9(02) VALUE ZEROS.         01780800
           05 W06-RES-MSG                PIC X(70) VALUE SPACES.        01781000
           05 W06-REF-NO                 PIC X(20) VALUE SPACES.        01781177
           05 W06-HD-ORG-REPLYQ          PIC X(48) VALUE SPACES.        01781228
           05 W06-HD-ORG-REPLYQMGR       PIC X(48) VALUE SPACES.        01781328
           05 W06-ACCT-NO                PIC X(25) VALUE SPACES.        01781500
           05 W06-TOTAL-BR-REC           PIC 9(04) VALUE ZEROS.         01781680
           05 W06-LAST-SEQ-NUM           PIC 9(04) VALUE ZEROS.         01781773
           05 W06-LAST-IN-SEQ            PIC X(01) VALUE ZEROS.         01781872
           05 W06-NO-REC-PER-MSG         PIC 9(04) VALUE ZEROS.         01781984
           05 W06-OUTPUT-DET OCCURS 1 TO 50                             01782083
              DEPENDING ON TOTAL-ACCT INDEXED BY OUT-IDX.               01782183
              10 W06-DET-REC-TYPE        PIC X(01) VALUE 'S'.           01782283
              10 W06-DET-SEQ-NO          PIC 9(04) VALUE ZEROS.         01782383
              10 W06-DET-BR-NO           PIC 9(04) VALUE ZEROS.         01782483
              10 W06-DET-TOTAL-SETUP     PIC X(06) VALUE ZEROS.         01782583
              10 W06-DET-TOTAL-RETURN    PIC X(06) VALUE ZEROS.         01783072
              10 W06-DET-TOTAL-PAID      PIC X(06) VALUE ZEROS.         01784072
              10 W06-DET-TOTAL-ALL       PIC X(06) VALUE ZEROS.         01785072
      *                                                                 01785118
       01  W07-FILE-OUT.                                                01785214
           05  W07-TRANSID          PIC X(04).                          01785314
           05  W07-ACCT-NO          PIC X(10).                          01785414
           05  W07-TRANS-DATE       PIC X(08).                          01785514
           05  W07-SERIAL-NO        PIC X(05).                          01785614
      * for branch1k                                                    01785794
      *    05  W07-SETUP-BRANCH     PIC X(03).                          01785894
           05  W07-SETUP-BRANCH     PIC X(04).                          01785994
      * end branch1k                                                    01786094
           05  W07-SETUP-SUM-ITEM   PIC 9(07).                          01786194
           05  W07-PAID-SUM-ITEM    PIC 9(07).                          01786294
           05  W07-RETURN-SUM-ITEM  PIC 9(07).                          01786394
      * for branch1k                                                    01786494
      *    05  FILLER               PIC X(549) VALUE SPACES.            01786594
           05  FILLER               PIC X(548) VALUE SPACES.            01786694
      * end branch1k                                                    01786794
      *                                                                 01786894
       01  M05-LOG-MSG.                                                 01786994
           05  M05-TRANSACTION      PIC X(04).                          01787094
           05                       PIC X(01) VALUE SPACE.              01787194
           05  M05-TASK-NUMBER      PIC 9(09).                          01787294
           05                       PIC X(01) VALUE SPACE.              01788000
           05  M05-DATE.                                                01789000
               10  M05-YYYY         PIC 9(4).                           01790000
               10  FILLER           PIC X     VALUE '/'.                01800000
               10  M05-MM           PIC 9(2).                           01810000
               10  FILLER           PIC X     VALUE '/'.                01820000
               10  M05-DD           PIC 9(2).                           01821000
           05                       PIC X(01) VALUE SPACE.              01822000
           05  M05-TIME             PIC X(08).                          01823000
           05                       PIC X(01) VALUE SPACE.              01824000
           05  M05-DATA             PIC X(2000) VALUE SPACE.            01825000
       01  PASS-AREA.                                                   01826000
           05  PASS-INPUT               PIC 9(06) VALUE ZERO.           01827000
           05  PASS-RESULT              PIC 9(01) VALUE ZERO.           01828000
           05  PASS-OUTPUT              PIC 9(08) VALUE ZERO.           01829000
      *                                                                 02000000
      * DFHAID defines the standard attention identifiers (AIDs).       02010000
      *                                                                 02020000
       COPY DFHAID.                                                     02030000
      *                                                                 02040000
      *                                                                 02050000
      *===============================================================* 02060000
       LINKAGE SECTION.                                                 02070000
      *===============================================================* 02080000
      * COMMAREA 32000 BYTE                                             02090000
      *                                                                 02100000
       01  DFHCOMMAREA.                                                 02121000
           05  COMM-INPUT.                                              02121100
               10  CIN-REC-TYPE             PIC X(01).                  02121200
               10  CIN-TRAN-CODE            PIC X(06).                  02121300
               10  CIN-CHANNEL-ID           PIC X(04).                  02121400
               10  CIN-REF-NO               PIC X(20).                  02121577
               10  CIN-TRAN-DESC            PIC X(35).                  02121600
               10  CIN-HD-ORG-REPLYQ        PIC X(48).                  02121728
               10  CIN-HD-ORG-REPLYQMGR     PIC X(48).                  02121828
               10  CIN-ACCT-NO              PIC X(25).                  02121900
               10  CIN-TRANS-FROM-DATE      PIC X(10).                  02122012
               10  CIN-TRANS-TO-DATE        PIC X(10).                  02122112
               10  CIN-TRANS-DATE           PIC X(10).                  02122212
               10  CIN-LAST-SEQ-NUM         PIC 9(04).                  02122328
               10  FILLER                   PIC X(31779).               02122677
           05  COMM-OUTPUT    REDEFINES COMM-INPUT.                     02122700
               10  COUT-LENGTH              PIC 9(9) BINARY.            02123000
               10  COUT-OUTAREA             PIC X(31996).               02124000
      *                                                                 02150000
      *                                                                 02160000
      ***************************************************************** 02170000
       PROCEDURE DIVISION.                                              02180000
      ***************************************************************** 02190000
      *                                                                 02330000
       000-CONTROL.                                                     02341000
           PERFORM 100-INITIAL-RTN               THRU 100-EXIT.         02342000
           PERFORM 200-MAINLINE                  THRU 200-EXIT.         02343000
           PERFORM 300-PGM-EXIT                  THRU 300-EXIT.         02343100
           GOBACK.                                                      02344000
       000-EXIT.  EXIT.                                                 02345000
      *****************                                                 02370000
       100-INITIAL-RTN.                                                 02380000
           EXEC  CICS HANDLE    ABEND      LABEL                        02390000
                     (9000-CICS-ERROR)                                  02400000
           END-EXEC.                                                    02410000
           MOVE CIN-HD-ORG-REPLYQ  TO W06-HD-ORG-REPLYQ.                02420000
           MOVE CIN-HD-ORG-REPLYQMGR TO W06-HD-ORG-REPLYQMGR.           02430000
           MOVE CIN-REF-NO         TO W06-REF-NO.                       02441000
           ADD  50                 TO W06-NO-REC-PER-MSG.               02441184
       100-EXIT. EXIT.                                                  02441900
      *****************                                                 02442000
       200-MAINLINE.                                                    02442100
           MOVE CIN-ACCT-NO(1:10)          TO BCI-ACCT-NO               02443125
      * for branch1k                                                    02443294
      *    MOVE CIN-ACCT-NO(1:3)           TO BCI-SETUP-BRANCH          02443394
           MOVE SPACES                     TO BCI-SETUP-BRANCH          02443494
      * end branch1k                                                    02443594
           MOVE CIN-TRANS-FROM-DATE        TO BCI-TRANS-FROM-DATE       02443694
           MOVE CIN-TRANS-TO-DATE          TO BCI-TRANS-TO-DATE         02443794
           MOVE CIN-TRANS-DATE             TO BCI-TRANS-DATE            02443894
           EXEC CICS LINK PROGRAM(BC-PROGRAM)                           02445454
               COMMAREA(BC-LINK) LENGTH(LENGTH OF BC-LINK)              02445554
               RESP(W03-RESP1) RESP2(W03-RESP2)                         02445654
           END-EXEC                                                     02445754
      *    MOVE 'S ' TO BCO-STATUS-FLAG                                 02446076
           PERFORM 1000-FORMAT-OUTPUT  THRU 1000-EXIT.                  02446136
       200-EXIT. EXIT.                                                  02446513
      *****************                                                 02447000
      *                                                                 02800000
       300-PGM-EXIT.                                                    02810000
           MOVE SPACES TO COUT-OUTAREA.                                 02812000
           MOVE LENGTH OF W06-OUTPUT TO COUT-LENGTH.                    02812100
           MOVE W06-OUTPUT  TO COUT-OUTAREA.                            02812400
           EXEC CICS RETURN END-EXEC.                                   02820000
       300-EXIT. EXIT.                                                  02821000
      *                                                                 05450100
      *****************                                                 05450200
       1000-FORMAT-OUTPUT.                                              05451000
           IF W03-RESP1 = DFHRESP(NORMAL)                               05455000
           EVALUATE TRUE                                                05455100
              WHEN BCO-STATUS-FLAG = 'S '                               05455233
                 MOVE 0000 TO W06-RES-CODE                              05455500
                 MOVE 0    TO W06-RES-TYPE                              05455600
      *          MOVE                       TO W06-RES-MSG              05455713
                 MOVE CIN-ACCT-NO(1:10)     TO W06-ACCT-NO              05455813
                 MOVE '0001'                TO W02-TRANSID              05455939
                 MOVE CIN-ACCT-NO(1:10)     TO W02-ACCT-NO              05456046
                 MOVE CIN-TRANS-DATE(1:4)   TO W02-TRANS-DATE(1:4)      05456243
                 MOVE CIN-TRANS-DATE(6:2)   TO W02-TRANS-DATE(5:2)      05456343
                 MOVE CIN-TRANS-DATE(9:2)   TO W02-TRANS-DATE(7:2)      05456443
                 MOVE '00001'               TO W02-SERIAL-NO            05456539
                 EXEC CICS STARTBR FILE(BC-FILENAME)                    05456839
                   RIDFLD(W02-RIDFLD) EQUAL                             05456939
                   RESP(W03-RESP1) RESP2(W03-RESP2)                     05457039
                 END-EXEC                                               05457139
      * READ UNWANTED DATA                                              05457439
                 PERFORM UNTIL CNT-LOOP >= CIN-LAST-SEQ-NUM             05457739
                         OR W03-RESP1 NOT = DFHRESP(NORMAL)             05457839
                   MOVE W02-RIDFLD(1:14)    TO TEMP-CHECK               05457947
                   EXEC CICS READNEXT FILE(BC-FILENAME)                 05458047
                     INTO(W07-FILE-OUT)                                 05458147
                     RIDFLD(W02-RIDFLD)                                 05458247
                     LENGTH(LENGTH OF W07-FILE-OUT)                     05458347
                     RESP(W03-RESP1) RESP2(W03-RESP2)                   05458447
                   END-EXEC                                             05458547
                   ADD 1 TO CNT-LOOP                                    05459039
                 END-PERFORM                                            05459139
                 IF W03-RESP1 = DFHRESP(ENDFILE) THEN                   05459263
                    MOVE '0' TO W06-LAST-SEQ-NUM                        05459363
                    PERFORM 300-PGM-EXIT THRU 300-EXIT                  05459463
                 END-IF                                                 05459563
                 IF W03-RESP1 NOT = DFHRESP(ENDFILE) AND                05459639
                    W03-RESP1 NOT = DFHRESP(NORMAL)  THEN               05459739
                    PERFORM 9999-FILE-ERROR THRU 9999-EXIT              05459897
                 END-IF                                                 05459939
      * READ NEXT DATA                                                  05460039
                 MOVE '0' TO CNT-LOOP                                   05460139
                 MOVE '0' TO W03-RESP1                                  05460271
                 PERFORM UNTIL CNT-LOOP >= 50                           05460339
                         OR STOP-FLAG = 'Y'                             05460442
                   MOVE W02-RIDFLD(1:14)    TO TEMP-CHECK               05460547
                   EXEC CICS READNEXT FILE(BC-FILENAME)                 05460639
                     INTO(W07-FILE-OUT)                                 05460739
                     RIDFLD(W02-RIDFLD)                                 05460839
                     LENGTH(LENGTH OF W07-FILE-OUT)                     05460939
                     RESP(W03-RESP1) RESP2(W03-RESP2)                   05461039
                   END-EXEC                                             05461139
                   IF W02-RIDFLD(1:14) NOT = TEMP-CHECK OR              05461963
                      W03-RESP1 = DFHRESP(ENDFILE) THEN                 05462064
                      MOVE 'Y' TO STOP-FLAG                             05462163
                   ELSE                                                 05462463
                      PERFORM 2000-FORMAT-FILE-OUT THRU 2000-EXIT       05462763
                      SET OUT-IDX UP BY 1                               05462863
                      ADD 1 TO CNT-LOOP                                 05462981
                   END-IF                                               05463063
                 END-PERFORM                                            05463263
                 MOVE CNT-LOOP TO W06-TOTAL-BR-REC                      05463363
                 COMPUTE  W06-LAST-SEQ-NUM = CIN-LAST-SEQ-NUM +         05463463
                                             CNT-LOOP END-COMPUTE       05463563
                 IF STOP-FLAG = 'Y' THEN                                05463664
                   MOVE '1' TO W06-LAST-IN-SEQ                          05463763
                 ELSE                                                   05463863
                   IF CNT-LOOP < 50 THEN                                05463963
                     PERFORM 9999-FILE-ERROR THRU 9999-EXIT             05464097
                   END-IF                                               05464163
                 END-IF                                                 05464263
              WHEN BCO-STATUS-FLAG = 'E '                               05464363
                 MOVE 9999 TO W06-RES-CODE                              05464485
                 MOVE 1    TO W06-RES-TYPE                              05464563
                 MOVE BCO-STATUS-DESCRIPTION TO W06-RES-MSG             05464663
              WHEN OTHER                                                05464763
                 MOVE 9999 TO W06-RES-CODE                              05464863
                 MOVE 1    TO W06-RES-TYPE                              05464963
           END-EVALUATE                                                 05465063
      *---********* start to handle error in case of---------           05465199
      *---connection between CICS BCM and BCS lost -------------------  05465298
           <USER>                                                         <GROUP>
              MOVE 9999                    TO W06-RES-CODE              05465499
              MOVE 1                       TO W06-RES-TYPE              05465599
              MOVE 'COMMUNICATION ERROR'   TO W06-RES-MSG               05465699
      *---********* end-------------------------------------------      05465799
           END-IF.                                                      05465863
       1000-EXIT.                                                       05465963
           EXIT.                                                        05466063
       2000-FORMAT-FILE-OUT.                                            05466163
           ADD 1 TO TOTAL-ACCT.                                         05466263
           COMPUTE W06-DET-SEQ-NO(OUT-IDX) = CIN-LAST-SEQ-NUM           05466363
                                  + CNT-LOOP + 1 END-COMPUTE.           05466463
           MOVE W07-SETUP-BRANCH                                        05466563
                TO W06-DET-BR-NO(OUT-IDX).                              05466663
           MOVE W07-SETUP-SUM-ITEM(2:6)                                 05466763
                TO W06-DET-TOTAL-SETUP(OUT-IDX).                        05466863
           MOVE W07-RETURN-SUM-ITEM(2:6)                                05466963
                TO W06-DET-TOTAL-RETURN(OUT-IDX).                       05467063
           MOVE W07-PAID-SUM-ITEM(2:6) TO W06-DET-TOTAL-PAID(OUT-IDX).  05467163
           ADD  W07-SETUP-SUM-ITEM  TO W07-RETURN-SUM-ITEM              05467263
                GIVING TEMP-NUM.                                        05467363
           ADD  W07-PAID-SUM-ITEM   TO TEMP-NUM.                        05467463
           MOVE TEMP-NUM(2:6)       TO W06-DET-TOTAL-ALL(OUT-IDX).      05467563
       2000-EXIT. EXIT.                                                 05467663
      *****************                                                 05467763
       9999-FILE-ERROR.                                                 05467897
           MOVE     9999                 TO  W06-RES-CODE.              05467997
           MOVE     1                    TO  W06-RES-TYPE.              05468063
           MOVE     'ERROR READING FILE' TO  W06-RES-MSG.               05468163
           PERFORM  300-PGM-EXIT         THRU 300-EXIT.                 05468263
           GOBACK.                                                      05468363
       9999-EXIT. EXIT.                                                 05468497
       9000-CICS-ERROR.                                                 05468563
           MOVE     9999               TO    W06-RES-CODE.              05468663
           MOVE     1                  TO    W06-RES-TYPE.              05468763
           MOVE     'CICS ERROR'       TO    W06-RES-MSG.               05468863
           PERFORM  300-PGM-EXIT       THRU  300-EXIT.                  05468963
           GOBACK.                                                      05469063
       9000-EXIT. EXIT.                                                 05470063
      *                                                                 05473700
      ***************************************************************** 05473800
      *        End of program BCBCS060                                * 05474025
      ***************************************************************** 05480000