 CBL XOPTS(COBOL2)                                                      00010000
 CBL MAP,OBJ,RENT,RES,DYNAM,OPT,LIB,DATA(31),LIST,APOST,TRUNC(BIN)      00020000
      ***************************************************************** 00030000
       IDENTIFICATION DIVISION.                                         00040000
      ***************************************************************** 00050000
       PROGRAM-ID.                      BCBCS061 .                      00060032
       DATE-COMPILED.                                                   00070000
      *REMARKS.                                                         00080000
      ***************************************************************** 00090000
      * DESCRIPTION :                                                 * 00100000
      *    PROGRAM BC DETAIL TRANSACTION INQUIRY                      * 00280034
      * FORMAT :                                                      * 00290000
      *    LINK VIA COMMAREA FROM BCMSMAIN                            * 00300000
      * AUTHOR :                                                      * 00310000
      *    TRIRAT   SUWANPRATEEB / IBM THAILAND                       * 00320000
      *                                                               * 01150000
      * MODIFY :                                                      * 01151081
      *    23/03/04 : EDIT BK-ID AND BR-ID CHANGE X TO 9 BY ATITANA   * 01152081
      *    24/03/04 : EDIT BC LAYOUT FILE ADD CHQ-DUE-DATE AND BC-NO  * 01153082
      *    25/03/04 : EXTEND INVOICE NO IN FILE OUT FROM 7 TO 100     * 01154087
      *               AND EXTEND FILE SIZE FROM 128 TO 300            * 01155088
      *    28/11/05 : EDIT LAYOUT FROM 300 BYTES TO 571 BYTES(ATITANA)* 01156089
      *                                                               * 01157091
      * MODIFIED :                                                    * 01158091
      * 07/08/06 :  UR49030004 -- BRANCH1K PROJECT.                   * 01159091
      *             MODIFIED BY RAPEEPHAN S.                          * 01159191
      * 15/10/20 :  P630024-02 -- DEVELOP NEW REPORT ENGINE           * 01159299
      *                           AND HISTORICAL DATA MODULES         * 01159399
      *             MODIFIED BY ITTICHOTE CH.                         * 01159499
      *                                                               * 01159599
      ***************************************************************** 01160000
      *                                                                 01170000
      *                                                                 01180000
      ***************************************************************** 01190000
       ENVIRONMENT DIVISION.                                            01200000
      ***************************************************************** 01210000
      *                                                                 01220000
      *                                                                 01230000
      ***************************************************************** 01240000
       DATA DIVISION.                                                   01250000
      ***************************************************************** 01260000
      *                                                                 01270000
      *===============================================================* 01280000
       WORKING-STORAGE SECTION.                                         01290000
      *===============================================================* 01300000
      *                                                                 01310000
       01  W00-EYECATCHER          PIC X(32)   VALUE                    01320000
                 '* ***** WORKING STORAGE ***** *'.                     01330000
      *                                                                 01340000
       01  W01-PROGID               PIC X(8) VALUE 'BCBCS061'.          01350032
      *                                                                 01370000
       01  W02-RIDFLD.                                                  01390043
           05 W02-TRANS-ID          PIC X(04) VALUE SPACES.             01400043
           05 W02-ACCT-NO           PIC X(10) VALUE SPACES.             01410043
           05 W02-TRANS-DATE        PIC X(08) VALUE SPACES.             01420043
           05 W02-SERIAL-NO         PIC X(05) VALUE SPACES.             01430043
       01  TEMP-CHECK               PIC X(14) VALUE SPACES.             01440043
      *                                                                 01510000
      * W03 holds the values for CICS commands.                         01520000
      *                                                                 01530000
       01  W03-RESP1                PIC S9(7) COMP.                     01550000
       01  W03-RESP2                PIC S9(7) COMP.                     01551000
       01  W03-LENGTHRETD           PIC S9(9) COMP.                     01560000
       01  W03-LENGTHSENT           PIC S9(9) COMP.                     01570000
       01  W03-SPACE                PIC X VALUE SPACE.                  01580000
      *                                                                 01590000
       01  BC-PROGRAM               PIC X(8) VALUE 'PBCS011 '.          01611033
       01  BC-FILENAME              PIC X(8) VALUE 'BCBCMDTL'.          01612033
       01  W04-PROBLEM-FLAG         PIC 9.                              01620000
       01  W04-NO-PROBLEM           PIC 9 VALUE 0.                      01630000
       01  W04-PROBLEM              PIC 9 VALUE 1.                      01640000
       01  CNTTEL                   PIC 9(4) COMP VALUE 0.              01640343
       01  STOP-FLAG                PIC X VALUE 'N'.                    01641000
       01  TOTAL-ACCT               PIC 9(6) VALUE ZEROS.               01641100
       01  CNT-LOOP                 PIC 9(6) VALUE ZEROS.               01641200
       01  CHARCOUNT                PIC 9999 VALUE ZEROS.               01641300
       01  ENDCOUNT                 PIC 9999 VALUE ZEROS.               01642000
      *>>> P630024-02                                                   01642199
       01  NEXT-FETCH-SEQ-NUM       PIC 9(04) VALUE ZEROS.              01642299
       01  FETCH-FILE-OUT.                                              01642399
           05  FETCH-TRANSID          PIC X(04).                        01642499
           05  FETCH-ACCT-NO          PIC X(10).                        01642599
           05  FETCH-TRANS-DATE       PIC X(08).                        01642699
           05  FETCH-SERIAL-NO        PIC 9(05).                        01642799
           05  FETCH-DETAIL           PIC X(573).                       01642899
      *<<< P630024-02                                                   01642999
       01  W04-CALL-ERROR-MSG.                                          01643000
           05 W04-CALL              PIC X(20) VALUE SPACE.              01643100
           05                       PIC X(11) VALUE                     01644000
                     'RESP  CODE:'.                                     01645000
           05 W04-RESP              PIC Z(08)9.                         01646000
           05                       PIC X(11) VALUE                     01647000
                     'RESP2 CODE:'.                                     01648000
           05 W04-RESP2             PIC Z(08)9.                         01649000
      *                                                                 01650000
       01  W05-TS-MESSAGE-LENGTH    PIC S9(4) BINARY.                   01651000
       01  W05-ABSTIME              PIC S9(15) COMP-3.                  01652000
       01  W05-DD                   PIC S9(8)  BINARY VALUE ZERO.       01653000
       01  W05-MM                   PIC S9(8)  BINARY VALUE ZERO.       01654000
       01  W05-YYYY                 PIC S9(8)  BINARY VALUE ZERO.       01655000
       01  W05-DATE                 PIC X(11)  VALUE  LOW-VALUES.       01656000
       01  W05-DATEFORM             PIC X(6)   VALUE  LOW-VALUES.       01657000
       01  W05-YYMMDD               PIC X(8)   VALUE  SPACES.           01658000
       01  W05-TIME                 PIC X(8)   VALUE  SPACES.           01659000
       01  W05-SERIAL-NO            PIC 9(05)  VALUE  00000.            01660099
      *                                                                 01770000
       01  BC-LINK.                                                     01770132
           05 BC-IN.                                                    01770232
            10 BCI-TRANS-ID              PIC X(04) VALUE '0002'.        01770332
            10 BCI-ACCT-NO               PIC X(10) VALUE SPACES.        01770432
      * for branch1k                                                    01770592
      *     10 BCI-SETUP-BRANCH          PIC X(03) VALUE SPACES.        01770692
            10 BCI-SETUP-BRANCH          PIC X(04) VALUE SPACES.        01770792
      * end branch1k                                                    01770892
            10 BCI-STATUS                PIC 9(01) VALUE ZERO.          01770992
            10 BCI-TRANS-FROM-DATE       PIC X(10) VALUE SPACES.        01771092
            10 BCI-TRANS-TO-DATE         PIC X(10) VALUE SPACES.        01771192
            10 BCI-TRANS-DATE            PIC X(10) VALUE SPACES.        01772092
      * for branch1k                                                    01773092
      *     10 FILLER                    PIC X(52) VALUE SPACES.        01774492
            10 FILLER                    PIC X(51) VALUE SPACES.        01774592
      * end branch1k                                                    01774692
           05 BC-OUT REDEFINES BC-IN.                                   01776754
            10 BCO-APPLICATION-NAME      PIC X(03).                     01776832
            10 BCO-STATUS-FLAG           PIC X(02).                     01776946
            10 BCO-STATUS-NUMBER         PIC X(05).                     01777032
            10 BCO-STATUS-DESCRIPTION    PIC X(50).                     01777132
            10 BCO-SQLSTATE              PIC X(05).                     01777232
            10 BCO-SQLCODE               PIC X(10).                     01777332
            10 FILLER                    PIC X(25).                     01780212
      *                                                                 01780300
       01 W06-OUTPUT.                                                   01780400
           05 W06-REC-TYPE               PIC X(01) VALUE 'H'.           01780500
           05 W06-TRAN-CODE              PIC X(06) VALUE 'BCS061'.      01780632
           05 W06-RES-CODE               PIC 9(04) VALUE ZEROS.         01780700
           05 W06-RES-TYPE               PIC 9(02) VALUE ZEROS.         01780800
           05 W06-RES-MSG                PIC X(70) VALUE SPACES.        01781000
           05 W06-REF-NO                 PIC X(20) VALUE SPACES.        01781166
           05 W06-HD-ORG-REPLYQ          PIC X(48) VALUE SPACES.        01781235
           05 W06-HD-ORG-REPLYQMGR       PIC X(48) VALUE SPACES.        01781335
           05 W06-ACCT-NO                PIC X(25) VALUE SPACES.        01781400
           05 W06-TOTAL-BR-REC           PIC 9(04) VALUE ZEROS.         01781550
           05 W06-LAST-SEQ-KEY           PIC 9(04) VALUE ZEROS.         01781635
           05 W06-LAST-IN-SEQ            PIC 9(01) VALUE ZEROS.         01781735
           05 W06-NO-REC-PER-MSG         PIC 9(04) VALUE ZEROS.         ********
           05 W06-OUTPUT-DET OCCURS 1 TO 50                             ********
              DEPENDING ON TOTAL-ACCT INDEXED BY OUT-IDX.               ********
              10 W06-DET-REC-TYPE        PIC X(01)    VALUE 'D'.        ********
              10 W06-DET-SEQ-NUM         PIC 9(04)    VALUE ZEROS.      ********
              10 W06-DET-DETAIL          PIC X(573)   VALUE SPACES.     ********
      *       10 W06-DET-CHQ-BANK-NO     PIC 9(03)    VALUE ZEROS.      ********
      *       10 W06-DET-CHQ-BR-NO       PIC 9(04)    VALUE ZEROS.      ********
      *       10 W06-DET-CHQ-NO          PIC X(07)    VALUE SPACES.     ********
      *       10 W06-DET-CHQ-AMT         PIC 9(14)V99 VALUE ZEROS.      ********
      *       10 W06-DET-CHQ-RCV-BR-NO   PIC 9(04)    VALUE ZEROS.      ********
      *       10 W06-DET-BC-REF-NO       PIC X(13)    VALUE SPACES.     ********
      *       10 W06-DET-BC-STATUS       PIC 9(01).                     ********
      *       10 W06-DET-LAST-TRN-DT.                                   ********
      *          15 W06-DET-LAST-TRN-YEAR  PIC X(04)  VALUE SPACES.     ********
      *          15 FILLER                 PIC X(01)  VALUE '-'.        ********
      *          15 W06-DET-LAST-TRN-MONTH PIC X(02)  VALUE SPACES.     ********
      *          15 FILLER                 PIC X(01)  VALUE '-'.        ********
      *          15 W06-DET-LAST-TRN-DAY   PIC X(02)  VALUE SPACES.     01785789
      *       10 W06-DET-BC-RETURN-NO    PIC X(02)    VALUE SPACES.     01785889
      *       10 W06-DET-CHQ-DUE-DT      PIC X(10)    VALUE SPACES.     01785989
      *       10 W06-DET-CHQ-FEE-AMT     PIC 9(14)V99 VALUE ZEROS.      01786089
      *       10 W06-DET-INVOICE-NO      PIC X(100)   VALUE SPACES.     01786189
      *                                                                 01786227
       01  W07-FILE-OUT.                                                01786327
           05  W07-TRANSID          PIC X(04).                          01786427
           05  W07-ACCT-NO          PIC X(10).                          01786527
           05  W07-TRANS-DATE       PIC X(08).                          01786627
           05  W07-SERIAL-NO        PIC 9(05).                          01786767
           05  W07-DETAIL           PIC X(573).                         01786890
      *    05  W07-BTRDATE          PIC X(08).                          01786989
      *    05  W07-BACNO            PIC X(11).                          01787089
      *    05  W07-BACNAME          PIC X(40).                          01787189
      *    05  W07-BCHQBKNA         PIC X(20).                          01787289
      *    05  W07-BCHQBRNA         PIC X(20).                          01787389
      *    05  W07-BCHQRBK          PIC X(02).                          01787489
      *    05  W07-BCHQRBR          PIC X(03).                          01787589
      *    05  W07-BCHQDATE         PIC X(08).                          01787689
      *    05  W07-BCHQNO           PIC X(07).                          01787789
      *    05  W07-BCHQAMT          PIC 9(16).                          01787889
      *    05  W07-BCHQCOM          PIC 9(16).                          01787989
      *    05  W07-BCHQNET          PIC 9(16).                          01788089
      *    05  W07-BREFNO           PIC X(10).                          01788189
      *    05  W07-BINVOICE         PIC X(100).                         01788289
      *    05  W07-BCODE            PIC X(40).                          01788389
      *    05  W07-BOTHER           PIC X(35).                          01788489
      *    05  W07-BCHQTYPE         PIC X(03).                          01788589
      *    05  W07-BSTATUS          PIC X(01).                          01788689
      *    05  W07-BCSDATE          PIC X(08).                          01788789
      *    05  W07-BSNDATE          PIC X(08).                          01788889
      *    05  W07-BCHQBK           PIC X(02).                          01788989
      *    05  W07-BCHQBR           PIC X(03).                          01789089
      *    05  W07-BSETBR           PIC X(03).                          01789189
      *    05  W07-BSETBRNA         PIC X(20).                          01789289
      *    05  W07-BRCVNA           PIC X(20).                          01789389
      *    05  W07-BCHQCOR          PIC X(30).                          01789489
      *    05  W07-BRETNO           PIC X(02).                          01789589
      *    05  W07-BRETTIME         PIC 9(03).                          01789689
      *    05  W07-BCINDATE         PIC X(08).                          01789789
      *    05  W07-BCEDATE          PIC X(02).                          01789889
      *    05  W07-BCDDATE          PIC X(02).                          01789989
      *    05  W07-BABCCOM          PIC 9(05).                          01790089
      *    05  W07-BCHQSTAT         PIC X(01).                          01790189
      *    05  W07-BCOLTYPE         PIC X(02).                          01790289
      *    05  W07-BSCBBKCD         PIC X(03).                          01790389
      *    05  W07-BRCVBRE          PIC X(03).                          01790489
      *    05  W07-BRCVBRNA         PIC X(20).                          01790589
      *    05  W07-FILLER           PIC X(72).                          01790689
      *                                                                 01790789
       01  M05-LOG-MSG.                                                 01791127
           05  M05-TRANSACTION      PIC X(04).                          01791227
           05                       PIC X(01) VALUE SPACE.              01791327
           05  M05-TASK-NUMBER      PIC 9(09).                          01791427
           05                       PIC X(01) VALUE SPACE.              01791527
           05  M05-DATE.                                                01791600
               10  M05-YYYY         PIC 9(4).                           01792000
               10  FILLER           PIC X     VALUE '/'.                01800000
               10  M05-MM           PIC 9(2).                           01810000
               10  FILLER           PIC X     VALUE '/'.                01820000
               10  M05-DD           PIC 9(2).                           01821000
           05                       PIC X(01) VALUE SPACE.              01822000
           05  M05-TIME             PIC X(08).                          01823000
           05                       PIC X(01) VALUE SPACE.              01824000
           05  M05-DATA             PIC X(2000) VALUE SPACE.            01825000
       01  PASS-AREA.                                                   01826000
           05  PASS-INPUT               PIC 9(06) VALUE ZERO.           01827000
           05  PASS-RESULT              PIC 9(01) VALUE ZERO.           01828000
           05  PASS-OUTPUT              PIC 9(08) VALUE ZERO.           01829000
      *                                                                 02000000
      * DFHAID defines the standard attention identifiers (AIDs).       02010000
      *                                                                 02020000
       COPY DFHAID.                                                     02030000
      *                                                                 02040000
      *                                                                 02050000
      *===============================================================* 02060000
       LINKAGE SECTION.                                                 02070000
      *===============================================================* 02080000
      * COMMAREA 32000 BYTE                                             02090000
      *                                                                 02100000
       01  DFHCOMMAREA.                                                 02121000
           05  COMM-INPUT.                                              02121100
               10  CIN-REC-TYPE             PIC X(01).                  02121200
               10  CIN-TRAN-CODE            PIC X(06).                  02121300
               10  CIN-CHANNEL-ID           PIC X(04).                  02121400
               10  CIN-REF-NO               PIC X(20).                  02121566
               10  CIN-TRAN-DESC            PIC X(35).                  02121600
               10  CIN-HD-ORG-REPLYQ        PIC X(48).                  02121734
               10  CIN-HD-ORG-REPLYQMGR     PIC X(48).                  02121834
               10  CIN-ACCT-NO              PIC X(25).                  02121900
               10  CIN-BR-NO                PIC 9(04).                  02122022
               10  CIN-BC-STATUS            PIC 9(01).                  02122173
               10  CIN-TRANS-FROM-DATE      PIC X(10).                  02122222
               10  CIN-TRANS-TO-DATE        PIC X(10).                  02122322
               10  CIN-TRANS-DATE           PIC X(10).                  02122422
               10  CIN-LAST-SEQ-NUM         PIC 9(04).                  02122534
               10  FILLER                   PIC X(31775).               02122675
           05  COMM-OUTPUT    REDEFINES COMM-INPUT.                     02122700
               10  COUT-LENGTH              PIC 9(9) BINARY.            02123000
               10  COUT-OUTAREA             PIC X(31996).               02124000
      *                                                                 02150000
      *                                                                 02160000
      ***************************************************************** 02170000
       PROCEDURE DIVISION.                                              02180000
      ***************************************************************** 02190000
      *                                                                 02330000
       000-CONTROL.                                                     02341000
                                                                        02341199
      *    EXEC CICS WRITEQ TS QUEUE('BCS061')                          02341299
      *         FROM(COMM-INPUT)                                        02341399
      *         NOHANDLE                                                02341499
      *    END-EXEC.                                                    02341599
                                                                        02341699
           PERFORM 100-INITIAL-RTN               THRU 100-EXIT.         02342000
      ***  PERFORM 200-MAINLINE                  THRU 200-EXIT.         02342199
           IF CIN-LAST-SEQ-NUM = 0000 THEN                              02342299
              PERFORM 200-MAINLINE               THRU 200-EXIT          02343099
           ELSE                                                         02343199
              PERFORM 4000-NEXT-FETCH            THRU 4000-EXIT         02343299
           END-IF.                                                      02343399
           PERFORM 300-PGM-EXIT                  THRU 300-EXIT.         02343499
           GOBACK.                                                      02344000
       000-EXIT.  EXIT.                                                 02345000
      *****************                                                 02370000
       100-INITIAL-RTN.                                                 02380000
           EXEC  CICS HANDLE    ABEND      LABEL                        02390000
                     (9000-CICS-ERROR)                                  02400000
           END-EXEC.                                                    02410000
           MOVE CIN-HD-ORG-REPLYQ  TO W06-HD-ORG-REPLYQ.                02420000
           MOVE CIN-HD-ORG-REPLYQMGR TO W06-HD-ORG-REPLYQMGR.           02430000
           MOVE CIN-REF-NO         TO W06-REF-NO.                       02441000
           ADD  50                 TO W06-NO-REC-PER-MSG.               02441177
       100-EXIT. EXIT.                                                  02441900
      *****************                                                 02442000
       200-MAINLINE.                                                    02442100
           MOVE CIN-ACCT-NO(1:10)          TO BCI-ACCT-NO               02443132
      * for branch1k                                                    02443292
      *    MOVE CIN-BR-NO(2:3)             TO BCI-SETUP-BRANCH          02443392
           MOVE CIN-BR-NO                  TO BCI-SETUP-BRANCH          02443492
      * end branch1k                                                    02443592
           MOVE CIN-BC-STATUS              TO BCI-STATUS                02443692
           MOVE CIN-TRANS-FROM-DATE        TO BCI-TRANS-FROM-DATE       02443792
           MOVE CIN-TRANS-TO-DATE          TO BCI-TRANS-TO-DATE         02443892
           MOVE CIN-TRANS-DATE             TO BCI-TRANS-DATE            02443992
           EXEC CICS LINK PROGRAM(BC-PROGRAM)                           02445472
               COMMAREA(BC-LINK) LENGTH(LENGTH OF BC-LINK)              02445572
               RESP(W03-RESP1) RESP2(W03-RESP2)                         02445672
           END-EXEC                                                     02445772
      *    MOVE 'S ' TO BCO-STATUS-FLAG                                 02445872
           PERFORM 1000-FORMAT-OUTPUT  THRU 1000-EXIT.                  02445942
       200-EXIT. EXIT.                                                  02446513
      *****************                                                 02447000
      *                                                                 02800000
       300-PGM-EXIT.                                                    02810000
           MOVE SPACES TO COUT-OUTAREA.                                 02812000
           MOVE LENGTH OF W06-OUTPUT TO COUT-LENGTH.                    02812100
           MOVE W06-OUTPUT  TO COUT-OUTAREA.                            02812400
                                                                        02812599
      *    EXEC CICS WRITEQ TS QUEUE('BCS061')                          02812699
      *         FROM(COUT-OUTAREA)                                      02812799
      *         NOHANDLE                                                02812899
      *    END-EXEC.                                                    02812999
                                                                        02813099
           EXEC CICS RETURN END-EXEC.                                   02820000
       300-EXIT. EXIT.                                                  02821000
      *                                                                 05450100
      *****************                                                 05450200
       1000-FORMAT-OUTPUT.                                              05451000
           IF W03-RESP1 = DFHRESP(NORMAL)                               05455000
           EVALUATE TRUE                                                05455100
              WHEN BCO-STATUS-FLAG = 'S '                               05455236
                 MOVE 0000 TO W06-RES-CODE                              05455300
                 MOVE 0    TO W06-RES-TYPE                              05455400
      *          MOVE                       TO W06-RES-MSG              05455513
                 MOVE CIN-ACCT-NO(1:10)     TO W06-ACCT-NO(1:10)        05455656
                 MOVE '0002'                TO W02-TRANS-ID             05455744
                 MOVE CIN-ACCT-NO(1:10)     TO W02-ACCT-NO              05455843
                 MOVE CIN-TRANS-DATE(1:4)   TO W02-TRANS-DATE(1:4)      05455943
                 MOVE CIN-TRANS-DATE(6:2)   TO W02-TRANS-DATE(5:2)      05456043
                 MOVE CIN-TRANS-DATE(9:2)   TO W02-TRANS-DATE(7:2)      05456143
                 MOVE '00001'               TO W02-SERIAL-NO            05456243
                 EXEC CICS STARTBR FILE(BC-FILENAME)                    05456332
                   RIDFLD(W02-RIDFLD) EQUAL                             05456448
                   RESP(W03-RESP1) RESP2(W03-RESP2)                     05456513
                 END-EXEC                                               05456613
      * READ UNWANTED DATA                                              05456736
                 PERFORM UNTIL CNT-LOOP >= CIN-LAST-SEQ-NUM             05456836
                         OR W03-RESP1 NOT = DFHRESP(NORMAL)             05456913
                   MOVE W02-RIDFLD(1:14)    TO TEMP-CHECK               05457143
                   EXEC CICS READNEXT FILE(BC-FILENAME)                 05457232
                     INTO(W07-FILE-OUT)                                 05457313
                     RIDFLD(W02-RIDFLD)                                 05457420
                     LENGTH(LENGTH OF W07-FILE-OUT)                     05457513
                     RESP(W03-RESP1) RESP2(W03-RESP2)                   05457615
                   END-EXEC                                             05457713
                   ADD 1 TO CNT-LOOP                                    05457915
                 END-PERFORM                                            05458013
                 IF W03-RESP1 = DFHRESP(ENDFILE) THEN                   05458161
                    MOVE '0' TO W06-LAST-SEQ-KEY                        05458263
                    PERFORM 300-PGM-EXIT THRU 300-EXIT                  05458362
                 END-IF                                                 05458461
                 IF W03-RESP1 NOT = DFHRESP(ENDFILE) AND                05459023
                    W03-RESP1 NOT = DFHRESP(NORMAL) THEN                05459161
                   PERFORM 9999-FILE-ERROR THRU 9999-EXIT               05460095
                 END-IF                                                 05461014
      * READ NEXT DATA                                                  05461136
                 MOVE '0' TO CNT-LOOP                                   05461236
                 PERFORM UNTIL CNT-LOOP >= 50                           05461336
                         OR STOP-FLAG = 'Y'                             05461543
                   MOVE W02-RIDFLD(1:14)    TO TEMP-CHECK               05461643
                   EXEC CICS READNEXT FILE(BC-FILENAME)                 05461736
                     INTO(W07-FILE-OUT)                                 05461836
                     RIDFLD(W02-RIDFLD)                                 05461936
                     LENGTH(LENGTH OF W07-FILE-OUT)                     05462036
                     RESP(W03-RESP1) RESP2(W03-RESP2)                   05462136
                   END-EXEC                                             05462236
                   IF W02-RIDFLD(1:14) NOT = TEMP-CHECK OR              05462360
                      W03-RESP1 = DFHRESP(ENDFILE) THEN                 05462465
                      MOVE 'Y' TO STOP-FLAG                             05462543
                   ELSE                                                 05462643
                      PERFORM 2000-FORMAT-FILE-OUT THRU 2000-EXIT       05463184
                      SET OUT-IDX UP BY 1                               05463484
                      ADD 1 TO CNT-LOOP                                 05463584
                   END-IF                                               05463684
                 END-PERFORM                                            05464599
                 MOVE CNT-LOOP              TO W06-TOTAL-BR-REC         05464699
                 COMPUTE  W06-LAST-SEQ-KEY = CIN-LAST-SEQ-NUM +         05464799
                                             CNT-LOOP END-COMPUTE       05464899
      *>>> P630024-02                                                   05464999
                 IF CNT-LOOP = 50                                       05465099
                      MOVE  W06-LAST-SEQ-KEY  TO NEXT-FETCH-SEQ-NUM     05465199
                      PERFORM 5000-FETCH-FOR-NEXT-REQ THRU 5000-EXIT    05465299
                 END-IF                                                 05465399
      *<<< P630024-02                                                   05465499
                                                                        05465599
                 IF STOP-FLAG = 'Y' THEN                                05465699
                   MOVE '1' TO W06-LAST-IN-SEQ                          05465799
                 ELSE                                                   05465899
                   IF CNT-LOOP < 50 THEN                                05465999
                     PERFORM 9999-FILE-ERROR THRU 9999-EXIT             05466099
                   END-IF                                               05466199
                 END-IF                                                 05466299
              WHEN BCO-STATUS-FLAG = 'E '                               05466399
                 MOVE 9999 TO W06-RES-CODE                              05466499
                 MOVE 1    TO W06-RES-TYPE                              05466599
                 MOVE BCO-STATUS-DESCRIPTION TO W06-RES-MSG             05466699
              WHEN OTHER                                                05466784
                 MOVE 9999 TO W06-RES-CODE                              05466884
                 MOVE 1    TO W06-RES-TYPE                              05466984
           END-EVALUATE                                                 05467084
      *---********* start to handle error in case of---------           05467197
      *---connection between CICS BCM and BCS lost ----------           05467297
           <USER>                                                         <GROUP>
              MOVE 9999                    TO W06-RES-CODE              05467499
              MOVE 1                       TO W06-RES-TYPE              05467599
              MOVE 'COMMUNICATION ERROR'   TO W06-RES-MSG               05467699
      *---********* end--------------------------------------           05467798
           END-IF.                                                      05467884
       1000-EXIT.                                                       05467984
           EXIT.                                                        05468084
                                                                        05468199
      ***<<<<<<<<<<<<<<<<<<<FETCH NEXT>>>>>>>>>>>>>>>>>>>>>>>>>>>>>     05468299
       4000-NEXT-FETCH.                                                 05468399
            INITIALIZE W05-SERIAL-NO.                                   05468499
            MOVE 0000                  TO W06-RES-CODE.                 05468599
            MOVE 0                     TO W06-RES-TYPE.                 05468699
            MOVE CIN-ACCT-NO(1:10)     TO W06-ACCT-NO(1:10).            05468799
            MOVE '0002'                TO W02-TRANS-ID.                 05468899
            MOVE CIN-ACCT-NO(1:10)     TO W02-ACCT-NO.                  05468999
            MOVE CIN-TRANS-DATE(1:4)   TO W02-TRANS-DATE(1:4).          05469099
            MOVE CIN-TRANS-DATE(6:2)   TO W02-TRANS-DATE(5:2).          05469199
            MOVE CIN-TRANS-DATE(9:2)   TO W02-TRANS-DATE(7:2).          05469299
            COMPUTE W05-SERIAL-NO = CIN-LAST-SEQ-NUM + 1.               05469399
            MOVE W05-SERIAL-NO         TO W02-SERIAL-NO.                05469499
                                                                        05469699
            EXEC CICS STARTBR FILE(BC-FILENAME)                         05469799
              RIDFLD(W02-RIDFLD) EQUAL                                  05469899
              RESP(W03-RESP1) RESP2(W03-RESP2)                          05469999
            END-EXEC.                                                   05470099
                                                                        05471099
      * READ NEXT DATA                                                  05471899
            MOVE '0'                   TO CNT-LOOP.                     05471999
            PERFORM UNTIL CNT-LOOP >= 50                                05472099
                    OR STOP-FLAG = 'Y'                                  05472199
              MOVE W02-RIDFLD(1:14)    TO TEMP-CHECK                    05472299
                                                                        05472399
              EXEC CICS READNEXT FILE(BC-FILENAME)                      05472499
                INTO(W07-FILE-OUT)                                      05472599
                RIDFLD(W02-RIDFLD)                                      05472699
                LENGTH(LENGTH OF W07-FILE-OUT)                          05472799
                RESP(W03-RESP1) RESP2(W03-RESP2)                        05472899
              END-EXEC                                                  05472999
                                                                        05473099
              IF W02-RIDFLD(1:14) NOT = TEMP-CHECK OR                   05473199
                 W03-RESP1 = DFHRESP(ENDFILE) THEN                      05473299
                 MOVE 'Y'              TO STOP-FLAG                     05473399
              ELSE                                                      05473499
                 PERFORM 2000-FORMAT-FILE-OUT THRU 2000-EXIT            05473599
                 SET OUT-IDX UP BY 1                                    05473699
                 ADD 1                 TO CNT-LOOP                      05473799
              END-IF                                                    05473899
                                                                        05473999
            END-PERFORM.                                                05474899
                                                                        05474999
            MOVE CNT-LOOP              TO W06-TOTAL-BR-REC.             05475099
            COMPUTE  W06-LAST-SEQ-KEY = CIN-LAST-SEQ-NUM +              05475199
                                        CNT-LOOP END-COMPUTE.           05475299
                                                                        05475399
      *>>> P630024-02                                                   05475499
              IF CNT-LOOP = 50                                          05475599
                   MOVE  W06-LAST-SEQ-KEY  TO NEXT-FETCH-SEQ-NUM        05475699
                   PERFORM 5000-FETCH-FOR-NEXT-REQ THRU 5000-EXIT       05475799
              END-IF                                                    05475899
      *>>> P630024-02                                                   05475999
                                                                        05476099
            IF STOP-FLAG = 'Y' THEN                                     05476199
              MOVE '1'                 TO W06-LAST-IN-SEQ               05476299
            ELSE                                                        05476399
              IF CNT-LOOP < 50 THEN                                     05476499
                PERFORM 9999-FILE-ERROR THRU 9999-EXIT                  05476599
              END-IF                                                    05476699
            END-IF.                                                     05476799
                                                                        05476899
       4000-EXIT.                                                       05476999
           EXIT.                                                        05477099
      ***<<<<<<<<<<<<<<<<<<<FETCH NEXT END>>>>>>>>>>>>>>>>>>>>>>>>>     05477199
                                                                        05477299
       2000-FORMAT-FILE-OUT.                                            05477399
           ADD 1 TO TOTAL-ACCT.                                         ********
           COMPUTE W06-DET-SEQ-NUM(OUT-IDX) = CIN-LAST-SEQ-NUM          ********
                                   + CNT-LOOP + 1 END-COMPUTE.          ********
           MOVE W07-DETAIL                                              ********
             TO W06-DET-DETAIL(OUT-IDX).                                ********
      *    MOVE W07-CHQ-BK                                              ********
      *      TO W06-DET-CHQ-BANK-NO(OUT-IDX).                           ********
      *    MOVE W07-CHQ-BR                                              ********
      *      TO W06-DET-CHQ-BR-NO(OUT-IDX).                             ********
      *    MOVE W07-CHQ-NO                                              ********
      *      TO W06-DET-CHQ-NO(OUT-IDX).                                ********
      *    MOVE W07-CHQ-AMT                                             ********
      *      TO W06-DET-CHQ-AMT(OUT-IDX).                               ********
      * ?????????                                                       ********
      *    MOVE W07-CHQ-COLLECT-BR                                      ********
      *      TO W06-DET-CHQ-RCV-BR-NO(OUT-IDX).                         ********
      *    MOVE W07-BCNO                                                ********
      *      TO W06-DET-BC-REF-NO(OUT-IDX).                             ********
      * ????????????                                                    05479299
      *    MOVE W07-STATUS                                              05479399
      *      TO W06-DET-BC-STATUS(OUT-IDX).                             05479499
      *    MOVE W07-TRANS-DATE(1:4)                                     05479599
      *      TO W06-DET-LAST-TRN-YEAR(OUT-IDX).                         05479699
      *    MOVE W07-TRANS-DATE(5:2)                                     05479799
      *      TO W06-DET-LAST-TRN-MONTH(OUT-IDX).                        05479899
      *    MOVE W07-TRANS-DATE(7:2)                                     05479999
      *      TO W06-DET-LAST-TRN-DAY(OUT-IDX).                          05480099
      *    MOVE W07-CHQ-RETURN-NO                                       05480199
      *      TO W06-DET-BC-RETURN-NO(OUT-IDX).                          05480299
      *    MOVE W07-????????                                            05480399
      *      TO W06-DET-CHQ-DUE-DT(OUT-IDX).                            05480499
      *    MOVE W07-CHQ-COMM                                            05480599
      *      TO W06-DET-CHQ-FEE-AMT(OUT-IDX).                           05480699
      *    MOVE W07-CHQ-DUED                                            05480799
      *      TO W06-DET-CHQ-DUE-DT(OUT-IDX).                            05480899
      *    MOVE W07-CHQ-INVOICE                                         05480999
      *      TO W06-DET-INVOICE-NO(OUT-IDX).                            05481099
       2000-EXIT. EXIT.                                                 05481199
      *****************                                                 05481299
                                                                        05481399
      *<<< P630024-02                                                   05481499
      *    CHECK FOR SET FLAG NEXT REQ                                  05481599
       5000-FETCH-FOR-NEXT-REQ.                                         05481699
            INITIALIZE W05-SERIAL-NO.                                   05481799
      **    MOVE CIN-ACCT-NO(1:10)     TO W06-ACCT-NO(1:10).            05481899
            MOVE '0002'                TO W02-TRANS-ID.                 05481999
            MOVE CIN-ACCT-NO(1:10)     TO W02-ACCT-NO.                  05482099
            MOVE CIN-TRANS-DATE(1:4)   TO W02-TRANS-DATE(1:4).          05482199
            MOVE CIN-TRANS-DATE(6:2)   TO W02-TRANS-DATE(5:2).          05482299
            MOVE CIN-TRANS-DATE(9:2)   TO W02-TRANS-DATE(7:2).          05482399
            COMPUTE W05-SERIAL-NO = NEXT-FETCH-SEQ-NUM + 1.             05482499
            MOVE W05-SERIAL-NO         TO W02-SERIAL-NO.                05482599
                                                                        05482999
            EXEC CICS READ FILE(BC-FILENAME)                            05483099
              INTO(FETCH-FILE-OUT)                                      05483199
              RIDFLD(W02-RIDFLD)                                        05483299
              LENGTH(LENGTH OF FETCH-FILE-OUT)                          05483399
              RESP(W03-RESP1) RESP2(W03-RESP2)                          05483499
            END-EXEC.                                                   05483999
                                                                        05484099
            IF W03-RESP1 NOT= DFHRESP(NORMAL)                           05484199
               MOVE 'Y'              TO STOP-FLAG                       05484299
            END-IF                                                      05484399
                                                                        05484499
            IF STOP-FLAG = 'Y' THEN                                     05486099
               MOVE '1'              TO W06-LAST-IN-SEQ                 05486199
            END-IF.                                                     05486299
                                                                        05486399
       5000-EXIT.                                                       05486499
           EXIT.                                                        05486599
      *<<< P630024-02                                                   05486699
                                                                        05486899
       9999-FILE-ERROR.                                                 05486999
           MOVE     9999                 TO  W06-RES-CODE.              05487099
           MOVE     1                    TO  W06-RES-TYPE.              05487199
           MOVE     'ERROR READING FILE' TO  W06-RES-MSG.               05487299
           PERFORM  300-PGM-EXIT         THRU 300-EXIT.                 05487399
           GOBACK.                                                      05487499
       9999-EXIT. EXIT.                                                 05487599
       9000-CICS-ERROR.                                                 05487699
           MOVE     9999               TO    W06-RES-CODE.              05487799
           MOVE     1                  TO    W06-RES-TYPE.              05487899
           MOVE     'CICS ERROR'       TO    W06-RES-MSG.               05487999
           PERFORM  300-PGM-EXIT       THRU  300-EXIT.                  05488099
           GOBACK.                                                      05488199
       9000-EXIT. EXIT.                                                 05488299
      *                                                                 05488399
      ***************************************************************** 05488499
      *        End of program BCBCS061                                * 05489099
      ***************************************************************** 05490099