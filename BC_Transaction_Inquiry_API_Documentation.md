# BC Transaction Inquiry System - API Documentation

## Overview
This document provides detailed request/response structures and table field mappings for the BC Transaction Inquiry System's two main processes.

## System Architecture
- **BCBCS060**: Summary Transaction Inquiry
- **BCBCS061**: Detail Transaction Inquiry  
- **PBCS011**: Common Database Processing Module

---

## Process 1: BCBCS060 - Summary Transaction Inquiry

### Request Structure (COMM-INPUT)
```cobol
01  COMM-INPUT.
    05  CIN-REC-TYPE             PIC X(01)    -- Record Type
    05  CIN-TRAN-CODE            PIC X(06)    -- Transaction Code: 'BCS060'
    05  CIN-CHANNEL-ID           PIC X(04)    -- Channel ID
    05  CIN-REF-NO               PIC X(20)    -- Reference Number
    05  CIN-TRAN-DESC            PIC X(35)    -- Transaction Description
    05  CIN-HD-ORG-REPLYQ        PIC X(48)    -- Original Reply Queue
    05  CIN-HD-ORG-REPLYQMGR     PIC X(48)    -- Original Reply Queue Manager
    05  CIN-ACCT-NO              PIC X(25)    -- Account Number (10 digits used)
    05  CIN-TRANS-FROM-DATE      PIC X(10)    -- Transaction From Date (YYYY-MM-DD)
    05  CIN-TRANS-TO-DATE        PIC X(10)    -- Transaction To Date (YYYY-MM-DD)
    05  CIN-TRANS-DATE           PIC X(10)    -- Specific Transaction Date
    05  CIN-LAST-SEQ-NUM         PIC 9(04)    -- Last Sequence Number
    05  FILLER                   PIC X(31779) -- Reserved space
```

### Response Structure (COMM-OUTPUT)
```cobol
01  W06-OUTPUT.
    05  W06-REC-TYPE               PIC X(01)    -- 'H' (Header)
    05  W06-TRAN-CODE              PIC X(06)    -- 'BCS060'
    05  W06-RES-CODE               PIC 9(04)    -- Response Code (0000=Success, 9999=Error)
    05  W06-RES-TYPE               PIC 9(02)    -- Response Type (0=Success, 1=Error)
    05  W06-RES-MSG                PIC X(70)    -- Response Message
    05  W06-REF-NO                 PIC X(20)    -- Reference Number
    05  W06-HD-ORG-REPLYQ          PIC X(48)    -- Original Reply Queue
    05  W06-HD-ORG-REPLYQMGR       PIC X(48)    -- Original Reply Queue Manager
    05  W06-ACCT-NO                PIC X(25)    -- Account Number
    05  W06-TOTAL-BR-REC           PIC 9(04)    -- Total Branch Records
    05  W06-LAST-SEQ-NUM           PIC 9(04)    -- Last Sequence Number
    05  W06-LAST-IN-SEQ            PIC X(01)    -- Last in Sequence Flag
    05  W06-NO-REC-PER-MSG         PIC 9(04)    -- Number of Records per Message (50)
    05  W06-OUTPUT-DET OCCURS 1 TO 50 DEPENDING ON TOTAL-ACCT.
        10  W06-DET-REC-TYPE        PIC X(01)    -- 'S' (Summary)
        10  W06-DET-SEQ-NO          PIC 9(04)    -- Sequence Number
        10  W06-DET-BR-NO           PIC 9(04)    -- Branch Number
        10  W06-DET-TOTAL-SETUP     PIC X(06)    -- Total Setup Transactions
        10  W06-DET-TOTAL-RETURN    PIC X(06)    -- Total Return Transactions
        10  W06-DET-TOTAL-PAID      PIC X(06)    -- Total Paid Transactions
        10  W06-DET-TOTAL-ALL       PIC X(06)    -- Total All Transactions
```

### Database Table Mappings (via PBCS011)

#### SUMMARY Cursor - Aggregated Data
```sql
SELECT TYP, CHQ_BR_RCV, COUNT(*) AS ITEM
FROM (
    -- Setup Transactions (TYP='1')
    SELECT '1' AS TYP, CHQ_BR_RCV, COUNT(*) AS ITEM
    FROM HOST_CHEQUE_DETAIL
    WHERE AC_NO = :AC-NO
      AND SEND_DATE BETWEEN :TRANS-FROM-DATE AND :TRANS-TO-DATE
      AND SORT_TYPE IN (0,1,2)
      AND BCNO_STA NOT IN ('CDEL','PAID','RREP','RETC')
    
    UNION
    
    -- Paid Transactions (TYP='2')
    SELECT '2' AS TYP, CHQ_BR_RCV, COUNT(*) AS ITEM
    FROM HOST_CHEQUE_DETAIL
    WHERE AC_NO = :AC-NO
      AND PAID_DATE BETWEEN :TRANS-FROM-DATE AND :TRANS-TO-DATE
      AND BCNO_STA IN ('PAID','CREP')
    
    UNION
    
    -- Return Transactions (TYP='3')
    SELECT '3' AS TYP, CHQ_BR_RCV, COUNT(*) AS ITEM
    FROM HOST_CHEQUE_DETAIL
    WHERE AC_NO = :AC-NO
      AND RETC_DATE BETWEEN :TRANS-FROM-DATE AND :TRANS-TO-DATE
      AND BCNO_STA IN ('RREP','RETC')
) ORDER BY CHQ_BR_RCV, TYP
```

#### Field Mappings - Summary Process
| Source Field | Target Field | Description |
|--------------|--------------|-------------|
| CHQ_BR_RCV | W06-DET-BR-NO | Receiving Branch Number |
| COUNT(*) [TYP='1'] | W06-DET-TOTAL-SETUP | Setup Transaction Count |
| COUNT(*) [TYP='2'] | W06-DET-TOTAL-PAID | Paid Transaction Count |
| COUNT(*) [TYP='3'] | W06-DET-TOTAL-RETURN | Return Transaction Count |
| SUM(ALL TYPES) | W06-DET-TOTAL-ALL | Total All Transactions |

---

## Process 2: BCBCS061 - Detail Transaction Inquiry

### Request Structure (COMM-INPUT)
```cobol
01  COMM-INPUT.
    05  CIN-REC-TYPE             PIC X(01)    -- Record Type
    05  CIN-TRAN-CODE            PIC X(06)    -- Transaction Code: 'BCS061'
    05  CIN-CHANNEL-ID           PIC X(04)    -- Channel ID
    05  CIN-REF-NO               PIC X(20)    -- Reference Number
    05  CIN-TRAN-DESC            PIC X(35)    -- Transaction Description
    05  CIN-HD-ORG-REPLYQ        PIC X(48)    -- Original Reply Queue
    05  CIN-HD-ORG-REPLYQMGR     PIC X(48)    -- Original Reply Queue Manager
    05  CIN-ACCT-NO              PIC X(25)    -- Account Number (10 digits used)
    05  CIN-BR-NO                PIC 9(04)    -- Branch Number
    05  CIN-BC-STATUS            PIC 9(01)    -- BC Status Filter
    05  CIN-TRANS-FROM-DATE      PIC X(10)    -- Transaction From Date
    05  CIN-TRANS-TO-DATE        PIC X(10)    -- Transaction To Date
    05  CIN-TRANS-DATE           PIC X(10)    -- Specific Transaction Date
    05  CIN-LAST-SEQ-NUM         PIC 9(04)    -- Last Sequence Number (for pagination)
    05  FILLER                   PIC X(31775) -- Reserved space
```

### Response Structure (COMM-OUTPUT)
```cobol
01  W06-OUTPUT.
    05  W06-REC-TYPE               PIC X(01)    -- 'H' (Header)
    05  W06-TRAN-CODE              PIC X(06)    -- 'BCS061'
    05  W06-RES-CODE               PIC 9(04)    -- Response Code
    05  W06-RES-TYPE               PIC 9(02)    -- Response Type
    05  W06-RES-MSG                PIC X(70)    -- Response Message
    05  W06-REF-NO                 PIC X(20)    -- Reference Number
    05  W06-HD-ORG-REPLYQ          PIC X(48)    -- Original Reply Queue
    05  W06-HD-ORG-REPLYQMGR       PIC X(48)    -- Original Reply Queue Manager
    05  W06-ACCT-NO                PIC X(25)    -- Account Number
    05  W06-TOTAL-BR-REC           PIC 9(04)    -- Total Records in Response
    05  W06-LAST-SEQ-KEY           PIC 9(04)    -- Last Sequence Key
    05  W06-LAST-IN-SEQ            PIC 9(01)    -- Last in Sequence Flag (1=Last)
    05  W06-NO-REC-PER-MSG         PIC 9(04)    -- Records per Message (50)
    05  W06-OUTPUT-DET OCCURS 1 TO 50 DEPENDING ON TOTAL-ACCT.
        10  W06-DET-REC-TYPE        PIC X(01)    -- 'D' (Detail)
        10  W06-DET-SEQ-NUM         PIC 9(04)    -- Sequence Number
        10  W06-DET-DETAIL          PIC X(573)   -- Complete Detail Record
```
