       ID               DIVISION.
      *-------------------------------------------------------------*
      * PURPOSE      : INQUIRY BC REALTIME FOR BCM,                 *
      *                SUMMARY OR DETAIL OF TRANSACTION             *
      *-------------------------------------------------------------*
      * CREATED BY   : SUTHATIP SUPATTANASIRINAN.(KOY)              *
      * CREATED DATE : JAN 15, 2004                                 *
      * UPDATED DATE :                                              *
      *   2014-03-24 - R<PERSON>LACE TABLE BCSXBKBR WITH HOST_BKBR_ZONE   *
      *   2014-06-25 - CHEQUE RETURN NUMBER                         *
      *   2017-05-29 - OP60030006 EXCLUDE POST BY PAY-IN            *
      *   2017-09-12 - RC60080006 EXCLUDE CQM                       *
      *   2021-06-01 - OP64030001 SUPPORT 2 CLEARING ZONE           *
      *-------------------------------------------------------------*
       PROGRAM-ID.      PBCS011.
       ENVIRONMENT      DIVISION.
       DATA             DIVISION.
      *-------------------------------------------------------------*
       WORKING-STORAGE SECTION.
       77  WK-ERROR                    PIC X(1)   VALUE '0'.
       77  SERIAL-NOX                  PIC 9(05)  VALUE ZERO.
       77  BATCH-NOX                   PIC 9(02)  VALUE ZERO.
       77  WK-KREC                     PIC 9(05)  VALUE ZERO.
       77  WK-DREC                     PIC 9(05)  VALUE ZERO.
       77  WK-SAFE-JNL                 PIC X(4)   VALUE SPACE.
       77  HV-TERMID                   PIC X(4)   VALUE SPACE.
       77  HV-DOT                      PIC X      VALUE '.'.
       77  TWA-LENGTH                  PIC S9(04) COMP.
       77  IND-1                       PIC S9(04) COMP.
       77  IND-2                       PIC S9(04) COMP.
       77  IND-3                       PIC S9(04) COMP.
       77  WK-SUM-SETUP                PIC 9(07)  VALUE ZERO.
       77  WK-SUM-PAID                 PIC 9(07)  VALUE ZERO.
       77  WK-SUM-RETURN               PIC 9(07)  VALUE ZERO.
       77  WK-SAVE-BRANCH              PIC X(04)  VALUE SPACE.
       77  ASTATUS                     PIC X(1)   VALUE SPACE.
       77  OSTATUS                     PIC X(1)  VALUE SPACE.
       77  SSTATUS                     PIC X(1)  VALUE SPACE.
       77  CSTATUS                     PIC X(1)  VALUE SPACE.
       77  PSTATUS                     PIC X(1)  VALUE SPACE.
       77  RSTATUS                     PIC X(1)  VALUE SPACE.
       77  SER-LINE                    PIC 9(4)  VALUE 0.
      *-------------------------------------------------------------*
       01  WK-ZONE-CHARGE        PIC X(1).
       01  WK-AC-GROUP           PIC X(1).
       01  WK-TYPE-CAL           PIC X(1).
       01  WK-CHARGE-TYPE        PIC X(1).
       01  WK-COM-TYPE           PIC X(1).
       01  WK-CHRG-TYPE          PIC X(2).
       01  WK-TRAN-BASE          PIC S9(4)V9(2)  USAGE COMP-3.
       01  WK-NORM-BASE          PIC S9(3)V9(2)  USAGE COMP-3.
       01  WK-NORM-BASE-MIN      PIC S9(3)V9(2)  USAGE COMP-3.
       01  WK-NORM-BASE-MAX      PIC S9(14)V9(2) USAGE COMP-3.
       01  WK-VAL-BASE-AMT-RATE  PIC S9(3)V9(2)  USAGE COMP-3.
       01  WK-VAL-BASE-AMT-MIN   PIC S9(3)V9(2)  USAGE COMP-3.
       01  WK-VAL-BASE-AMT-MAX   PIC S9(14)V9(2) USAGE COMP-3.
       01  WK-VAL-BASE-TIER-NO   PIC X(1).
       01  WORK-SP.
           02  SP-APP-NAME            PIC  X(03).
           02  SP-STATUS-FLAG         PIC  X(02).
           02  SP-STATUS-NUMBER       PIC  X(05).
           02  SP-SQLSTATE            PIC  X(05).
           02  SP-SQLCODE             PIC  X(10).
           02  SP-NB-FLAG             PIC  X(02).
           02  SP-NAME                PIC  X(18).
      *-------------------------------------------------------------*
       01  TMP-INVOICE                 PIC X(160).
       01  FILLER  REDEFINES TMP-INVOICE.
           05  WK-INVOICE              PIC X(100).
           05  WK-INVOICE2             PIC X(60).
       01  TMP-DATE                    PIC X(10).
       01  FILLER  REDEFINES TMP-DATE.
           05  TMP-YYYY                PIC X(04).
           05  TMP-F1                  PIC X(01).
           05  TMP-MM                  PIC X(02).
           05  TMP-F2                  PIC X(01).
           05  TMP-DD                  PIC X(02).
       01  WK-TRAN-DATE                PIC X(08).
       01  FILLER  REDEFINES WK-TRAN-DATE.
           05  WK-YYYY                 PIC X(04).
           05  WK-MM                   PIC X(02).
           05  WK-DD                   PIC X(02).
       01  WDATE                    PIC X(10).
       01  FILLER  REDEFINES WDATE.
           05  W1-YY1                  PIC X(02).
           05  W1-YY2                  PIC X(02).
           05  W1-F1                   PIC X(01).
           05  W1-MM                   PIC X(02).
           05  W1-F2                   PIC X(01).
           05  W1-DD                   PIC X(02).
       01  W2-DATE.
           05  W2-DD                   PIC X(02).
           05  W2-F1                   PIC X(01).
           05  W2-MM                   PIC X(02).
           05  W2-F2                   PIC X(01).
           05  W2-YY                   PIC X(02).
       01  CHK-COR-CODE.
           05  CODE1         PIC X(30) VALUE
           '�Ţ���ѭ�����١��ͧ'.
           05  CODE2         PIC X(30) VALUE
           '�����������١��ͧ'.
           05  CODE3         PIC X(30) VALUE
           '��Ҥ�ü������Թ���١��ͧ'.
           05  CODE4         PIC X(30) VALUE
           '�ѹ��������١��ͧ'.
           05  CODE5         PIC X(30) VALUE
           '�١��Ңͤ׹'.
           05  CODE6         PIC X(30) VALUE
           '�ӹǹ�Թ���١��ͧ'.
           05  CODE7         PIC X(30) VALUE
           '�Ţ��������١��ͧ'.
           05  CODE8         PIC X(30) VALUE
           '���٭���'.
           05  CODE9         PIC X(30) VALUE
           '�Դ����Ңҵ鹷ҧ'.
      *-------------------------------------------------------------*
      *    FORMAT OF SUMMARY AND DETAIL FILE FOR BCM => LENGTH = 600
      *-------------------------------------------------------------*
       01  DET-REC.
           02 DET-KEY                  PIC X(27).
           02 FILLER REDEFINES DET-KEY.
              05  DTRAN-ID             PIC X(04).
              05  DACCT-NO             PIC X(10).
              05  DTRANS-DATE          PIC X(08).
              05  DSERIAL-NO           PIC X(05).
           02 REP-DETAIL               PIC X(573).
           02 REP-DETAIL1 REDEFINES REP-DETAIL.
              05  DSETUP-BRANCH        PIC 9(04).
              05  DSETUP-ITEM          PIC 9(07).
              05  DPAID-ITEM           PIC 9(07).
              05  DRETURN-ITEM         PIC 9(07).
              05  FILLER               PIC X(548).
           02 REP-DETAIL2 REDEFINES REP-DETAIL.
              05  DTRN-DATE            PIC X(08).
              05  DAC-NO               PIC X(11).
              05  DAC-NAME             PIC X(40).
              05  DCHQ-BK-NM           PIC X(20).
              05  DCHQ-BR-NM           PIC X(20).
              05  DCHQ-R-BK            PIC X(02).
              05  DCHQ-R-BR            PIC X(04).
              05  DCHQ-DATE            PIC X(08).
              05  DCHQ-NO              PIC X(07).
              05  DCHQ-AMT             PIC 9(13).99.
              05  DCHQ-COM             PIC 9(13).99.
              05  DCHQ-NET             PIC 9(13).99.
              05  DCHQ-REF-NO          PIC X(10).
              05  DINVOICE             PIC X(100).
              05  DCODE                PIC X(40).
              05  DOTHER               PIC X(35).
              05  DCHQ-TYPE            PIC X(03).
              05  DSTATUS              PIC X(01).
              05  DBCS-DATE            PIC X(08).
              05  DSEND-DATE           PIC X(08).
              05  DCHQ-BK              PIC X(02).
              05  DCHQ-BR              PIC X(04).
              05  DCHQ-BR-SET          PIC X(04).
              05  DSET-BR-NM           PIC X(20).
              05  DCHQ-R-BRNM          PIC X(20).
              05  DCHQ-COR             PIC X(30).
              05  DCHQ-RET-NO          PIC X(02).
              05  DCHQ-RET-TIME        PIC 9(03).
              05  DIN-DATE             PIC X(08).
              05  DBC-EDATE            PIC X(02).
              05  DBC-DDATE            PIC X(02).
              05  DABC-COM             PIC 9(01).999.
              05  DCHQ-STAT            PIC X(01).
              05  DCOL-TYPE            PIC X(02).
              05  DSCB-BK-CD           PIC X(03).
              05  DRCV-BR              PIC X(04).
              05  DRCV-BR-NM           PIC X(20).
              05  DCHQ-R-BK3           PIC X(03).
              05  DCHQ-NO8             PIC X(08).
              05  DCHQ-BK3             PIC X(03).
              05  DDISPLAY             PIC X(01).
              05  FILLER               PIC X(53).
       01  TEST-SUMMARY.
           05 TS-TRAN-ID               PIC X(04) VALUE '0001'.
           05 TS-AC-NO                 PIC X(10) VALUE '1113000018'.
           05 TS-SETUP-BRANCH          PIC X(04).
           05 TS-STATUS                PIC X(01) VALUE '2'.
           05 TS-TRANS-FROM-DATE       PIC X(10) VALUE '2017-09-05'.
           05 TS-TRANS-TO-DATE         PIC X(10) VALUE '2017-09-05'.
           05 TS-TRANS-DATE            PIC X(10) VALUE '2017-09-05'.
           05 FILLER                   PIC X(51).
       01  TEST-DETAIL.
           05 TD-TRAN-ID               PIC X(04) VALUE '0002'.
           05 TD-AC-NO                 PIC X(10) VALUE '1113000018'.
           05 TD-SETUP-BRANCH          PIC X(04) VALUE '0222'.
           05 TD-STATUS                PIC X(01) VALUE '1'.
           05 TD-TRANS-FROM-DATE       PIC X(10) VALUE '2021-05-27'.
           05 TD-TRANS-TO-DATE         PIC X(10) VALUE '2021-05-27'.
           05 TD-TRANS-DATE            PIC X(10) VALUE '2021-05-27'.
           05 FILLER                   PIC X(51).
       01  INREC.
           05 TRAN-ID                  PIC X(04).
           05 AC-NO                    PIC X(10).
           05 SETUP-BRANCH             PIC X(04).
           05 STATUS-CHQ               PIC X(01).
           05 TRANS-FROM-DATE          PIC X(10).
           05 TRANS-TO-DATE            PIC X(10).
           05 TRANS-DATE               PIC X(10).
           05 FILLER                   PIC X(51).
      *-------------------------------------------------------------*
      *    OUTPUT TO BCM => LENGTH = 100
      *-------------------------------------------------------------*
       01  OUTREC                      PIC X(100).
       01  FILLER REDEFINES OUTREC.
           05  APPL-NAME               PIC X(03).
           05  STATUS-FLAG             PIC X(02).
           05  STATUS-NUMBER           PIC X(05).
           05  STATUS-DESC             PIC X(50).
           05  SQL-STATE               PIC X(05).
           05  SQL-CODE                PIC X(10).
           05  FILLER                  PIC X(25).
      *-------------------------------------------------------------*
       01  TMP-SQL-CODE                PIC X(10).
       01  FILLER REDEFINES TMP-SQL-CODE.
           05  WK-SQL-CODE             PIC ZZZZZZZZZ-.
       01  TMP-SQL-STATE               PIC X(05).
       01  FILLER REDEFINES TMP-SQL-STATE.
           05  WK-SQL-STATE            PIC ZZZZ-.
       01  HV-VERIFY                   PIC X(16).
       01  NOT-FOUND                   PIC S9(04) VALUE +100 COMP.
       01  NO-DATA                     PIC S9(04) VALUE -501 COMP.
       01  END-FETCH                   PIC X(01)  VALUE 'N'.
       01  END-DEL                     PIC X(01)  VALUE 'N'.
       01  FIRST-FETCH                 PIC X(01)  VALUE 'Y'.
       01  WK-RECL                     PIC S9(4)  COMP VALUE +600.
       01  WK-CHQ-RCV-BK.
           05  XX-CHQ-RCV-BK1          PIC X(1).
           05  XX-CHQ-RCV-BK2          PIC X(2).
       01  WK-CHQ-NO.
           05  XX-CHQ-NO1              PIC X(1).
           05  XX-CHQ-NO2              PIC X(7).
       01  WK-CHQ-BK.
           05  XX-CHQ-BK1              PIC X(1).
           05  XX-CHQ-BK2              PIC X(2).
      *-------------------------------------------------------------*
      *    DATA FROM FETCHING HOST_CHEQUE_DETAIL
      *-------------------------------------------------------------*
       01  WK-CHQDTL1.
           05  WCHQ-BR-RCV             PIC X(04).
           05  WCHQ-BR-SET             PIC X(04).
           05  WCHQ-BK                 PIC X(03).
           05  WCHQ-BR                 PIC X(04).
           05  WCHQ-NO                 PIC X(08).
           05  WAC-NO                  PIC X(10).
           05  WAC-BR                  PIC X(04).
           05  WAC-NAME                PIC X(40).
           05  WAC-GROUP               PIC X(01).
      *    05  WABC-BEF-EDATE          PIC X(02).
      *    05  WABC-BEF-DDATE          PIC X(02).
           05  WAABC-IC-EDATE          PIC X(02).
           05  WAABC-IC-DDATE          PIC X(02).
           05  WAABC-CL-EDATE          PIC X(02).
           05  WAABC-CL-DDATE          PIC X(02).
           05  WAABC-ICBC-EDATE        PIC X(02).
           05  WAABC-ICBC-DDATE        PIC X(02).
           05  WAABC-OCBC-EDATE        PIC X(02).
           05  WAABC-OCBC-DDATE        PIC X(02).
           05  WGABC-IC-EDATE          PIC X(02).
           05  WGABC-IC-DDATE          PIC X(02).
           05  WGABC-CL-EDATE          PIC X(02).
           05  WGABC-CL-DDATE          PIC X(02).
           05  WGABC-ICBC-EDATE        PIC X(02).
           05  WGABC-ICBC-DDATE        PIC X(02).
           05  WGABC-OCBC-EDATE        PIC X(02).
           05  WGABC-OCBC-DDATE        PIC X(02).
           05  WINVOICE                PIC X(160).
           05  WCHQ-REF-NO             PIC X(10).
           05  WCODE                   PIC X(40).
           05  WOTHER                  PIC X(35).
           05  WCHQ-TYPE               PIC X(03).
           05  WCUST-DATE              PIC X(10).
           05  WSEND-DATE              PIC X(10).
           05  WCREC-DATE              PIC X(10).
           05  WPAID-DATE              PIC X(10).
           05  WRETC-DATE              PIC X(10).
           05  WRREP-DATE              PIC X(10).
           05  WIN-DATE                PIC X(10).
      *    05  WTYPE-BCDAY             PIC X(02).
           05  TYPE-BCDAY              PIC X(02).
           05  WCHQ-RCV-BK             PIC X(03).
           05  WCHQ-RCV-BR             PIC X(04).
           05  WCHQ-RET-NO             PIC X(02).
           05  WCHQ-COR                PIC X(01).
           05  WCHQ-RET-TIME           PIC S9(9) USAGE COMP.
           05  WCHQ-STAT               PIC X(04).
           05  WSTAT-TYPE              PIC X(01).
           05  WCHQ-DATE               PIC X(10).
           05  WBC-NO                  PIC X(13).
           05  WFINE-CHQ               PIC X(01).
      * OP64030001 -->
      *    05  W-BOTCLCODE-1           PIC X(05).
      *    05  W-BOTCLCODE-2           PIC X(05).
      * OP64030001 <--
       01  WK-CHQDTL2.
           05  WCHQ-AMT                PIC S9(14)V99 COMP-3.
           05  WCHQ-COM                PIC S9(14)V99 COMP-3.
           05  WBEF-COM                PIC S9(14)V99 COMP-3.
           05  WTMP-COM                PIC S9(14)V99 COMP-3.
           05  WCHQ-NET                PIC S9(14)V99 COMP-3.
           05  WABC-COM                PIC S9(02)V999 COMP-3.
           05  WSUM-ITEM               PIC S9(07) COMP-3.
      *-------------------------------------------------------------*
           EXEC SQL INCLUDE  SQLCA    END-EXEC.
      *-------------------------------------------------------------*
      * DECLARE CURSOR FOR SUMMARY TRANSACTION
      *    PAID TRANSACTION STATUS = 'PAID'
      *    RETURN TRANSACTION STATUS = 'RREP','RETC'
      *    SETUP TRANSACTION STATUS <> 'CDEL'
      *-------------------------------------------------------------*
           EXEC SQL
            DECLARE SUMMARY CURSOR FOR
            SELECT TYP
                  ,CHQ_BR_RCV
                  ,ITEM
            FROM
                  (SELECT  '1' AS TYP
                          ,CHQ_BR_RCV
                          ,COUNT(*) AS ITEM
                   FROM    HOST_CHEQUE_DETAIL
                   WHERE   AC_NO = :AC-NO        AND
                           SEND_DATE BETWEEN :TRANS-FROM-DATE AND
                                             :TRANS-TO-DATE   AND
                           SORT_TYPE IN (0,1,2)  AND
                           BCNO_STA NOT IN
                           ('CDEL','PAID','RREP','RETC')
      * OP60030006 -->
                           AND COALESCE(CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                           AND COALESCE(CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
                   GROUP BY CHQ_BR_RCV
                   UNION
                   SELECT  '2' AS TYP
                          ,CHQ_BR_RCV
                          ,COUNT(*) AS ITEM
                   FROM    HOST_CHEQUE_DETAIL
                   WHERE   AC_NO = :AC-NO        AND
                           PAID_DATE BETWEEN :TRANS-FROM-DATE AND
                                               :TRANS-TO-DATE   AND
                           SORT_TYPE IN (0,1,2)  AND
                           BCNO_STA IN ('PAID','CREP')
      * OP60030006 -->
                           AND COALESCE(CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                           AND COALESCE(CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
                   GROUP BY CHQ_BR_RCV
                   UNION
                   SELECT  '3' AS TYP
                          ,CHQ_BR_RCV
                          ,COUNT(*) AS ITEM
                   FROM    HOST_CHEQUE_DETAIL
                   WHERE   AC_NO = :AC-NO        AND
                           RETC_DATE BETWEEN :TRANS-FROM-DATE AND
                                             :TRANS-TO-DATE   AND
                           SORT_TYPE IN (1,2)    AND
                           BCNO_STA IN ('RREP','RETC')
      * OP60030006 -->
                           AND COALESCE(CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                           AND COALESCE(CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
                   GROUP BY CHQ_BR_RCV) AS TEMP
            ORDER BY CHQ_BR_RCV, TYP
           END-EXEC.
      *-------------------------------------------------------------*
      * DECLARE CURSOR FOR DETAIL OF PAID TRANSACTION
      *    STATUS OF RECORD IS 'PAID'
      *-------------------------------------------------------------*
           EXEC SQL
            DECLARE DETPAID CURSOR FOR
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CHQ_BR_RCV = :SETUP-BRANCH AND
                    A.PAID_DATE BETWEEN :TRANS-FROM-DATE  AND
                                          :TRANS-TO-DATE    AND
                    A.SORT_TYPE IN (0,1,2)       AND
                    A.BCNO_STA IN ('PAID ','CREP')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            ORDER BY A.CHQ_RCV_BR, A.CHQ_BK, A.CHQ_BR, A.CHQ_NO
           END-EXEC.
      *            ,A.TYPE_BCDAY
      *-------------------------------------------------------------*
      * DECLARE CURSOR FOR DETAIL OF RETURN TRANSACTION
      *    STATUS OF RECORD IS 'RREP','RETC'
      *-------------------------------------------------------------*
           EXEC SQL
            DECLARE DETRETURN CURSOR FOR
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,COALESCE(C.AC_NAME,'')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CHQ_BR_RCV = :SETUP-BRANCH AND
                    A.RETC_DATE BETWEEN :TRANS-FROM-DATE AND
                                        :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (1,2)   AND
                    A.BCNO_STA IN ('RREP','RETC')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            ORDER BY A.CHQ_RCV_BR, A.CHQ_BK, A.CHQ_BR, A.CHQ_NO
           END-EXEC.
      *            ,A.TYPE_BCDAY
      *-------------------------------------------------------------*
      * DECLARE CURSOR FOR DETAIL OF SETUP TRANSACTION
      *    STATUS OF RECORD <> 'CDEL','PAID','RREP','RETC'
      *-------------------------------------------------------------*
           EXEC SQL
            DECLARE DETSETUP CURSOR FOR
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CHQ_BR_RCV = :SETUP-BRANCH AND
                    A.SEND_DATE BETWEEN :TRANS-FROM-DATE AND
                                        :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (0,1,2)       AND
                    A.BCNO_STA NOT IN ('CDEL','PAID','RREP','RETC')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            ORDER BY A.CHQ_RCV_BR, A.CHQ_BK, A.CHQ_BR, A.CHQ_NO
           END-EXEC.
      *            ,A.TYPE_BCDAY
      *-------------------------------------------------------------*
      * DECLARE CURSOR FOR DETAIL OF ALL TRANSACTION
      *-------------------------------------------------------------*
           EXEC SQL
            DECLARE DETALLTRN CURSOR FOR
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CHQ_BR_RCV = :SETUP-BRANCH AND
                    A.SEND_DATE BETWEEN :TRANS-FROM-DATE AND
                                        :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (0,1,2)       AND
                    A.BCNO_STA NOT IN ('CDEL','PAID','RREP','RETC')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            UNION
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CHQ_BR_RCV = :SETUP-BRANCH AND
                    A.PAID_DATE BETWEEN :TRANS-FROM-DATE  AND
                                          :TRANS-TO-DATE    AND
                    A.SORT_TYPE IN (0,1,2)       AND
                    A.BCNO_STA IN ('PAID','CREP')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            UNION
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CHQ_BR_RCV = :SETUP-BRANCH AND
                    A.RETC_DATE BETWEEN :TRANS-FROM-DATE AND
                                          :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (1,2)         AND
                    A.BCNO_STA IN ('RREP','RETC')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            ORDER BY CHQ_RCV_BR, CHQ_BK, CHQ_BR, CHQ_NO
           END-EXEC.
      *            ,A.TYPE_BCDAY
      *            ,A.TYPE_BCDAY
      *            ,A.TYPE_BCDAY
      *-------------------------------------------------------------*
      * DECLARE CURSOR FOR DETAIL OF ALL TRANSACTION FOR DOWNLOAD
      *-------------------------------------------------------------*
           EXEC SQL
            DECLARE DETALLDWN CURSOR FOR
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.SEND_DATE BETWEEN :TRANS-FROM-DATE AND
                                        :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (0,1,2)       AND
                    A.BCNO_STA NOT IN ('CDEL','PAID','RREP','RETC')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            UNION
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.PAID_DATE BETWEEN :TRANS-FROM-DATE  AND
                                        :TRANS-TO-DATE    AND
                    A.SORT_TYPE IN (0,1,2)       AND
                    A.BCNO_STA IN ('PAID','CREP')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            UNION
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.RETC_DATE BETWEEN :TRANS-FROM-DATE AND
                                        :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (1,2)         AND
                    A.BCNO_STA IN ('RREP','RETC')
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            UNION
            SELECT  A.CHQ_BR_SET
                   ,A.CHQ_BK
                   ,A.CHQ_BR
                   ,A.CHQ_NO
                   ,A.AC_NO
                   ,A.AC_BR
                   ,COALESCE(C.AC_NAME,'')
                   ,COALESCE(C.AC_GROUP,'N')
                   ,COALESCE(C.ABC_IC_DDATE,'0')
                   ,COALESCE(C.ABC_IC_EDATE,'0')
                   ,COALESCE(C.ABC_CL_DDATE,'0')
                   ,COALESCE(C.ABC_CL_EDATE,'0')
                   ,COALESCE(C.ABC_ICBC_DDATE,'0')
                   ,COALESCE(C.ABC_ICBC_EDATE,'0')
                   ,COALESCE(C.ABC_OCBC_DDATE,'0')
                   ,COALESCE(C.ABC_OCBC_EDATE,'0')
                   ,COALESCE(D.ABC_IC_DDATE,'0')
                   ,COALESCE(D.ABC_IC_EDATE,'0')
                   ,COALESCE(D.ABC_CL_DDATE,'0')
                   ,COALESCE(D.ABC_CL_EDATE,'0')
                   ,COALESCE(D.ABC_ICBC_DDATE,'0')
                   ,COALESCE(D.ABC_ICBC_EDATE,'0')
                   ,COALESCE(D.ABC_OCBC_DDATE,'0')
                   ,COALESCE(D.ABC_OCBC_EDATE,'0')
      *            ,C.ABC_BEF_EDATE
      *            ,C.ABC_BEF_DDATE
      *            ,C.ABC_COM
                   ,COALESCE(A.CHQ_AMT,0)
                   ,COALESCE(A.CHQ_COM,0)
                   ,COALESCE(A.BEF_COM,0)
                   ,COALESCE(B.INVOICE,'')
                   ,COALESCE(B.CODE,'')
                   ,COALESCE(B.OTHER,'')
                   ,A.BCNO_OTHBK
                   ,COALESCE(A.CHQ_TYPE,'')
                   ,COALESCE(B.SEND_DATE,A.SEND_DATE)
                   ,COALESCE(A.SEND_DATE,'2001-11-11')
                   ,COALESCE(A.CREC_DATE,'2001-11-11')
                   ,COALESCE(A.PAID_DATE,'2001-11-11')
                   ,COALESCE(A.RETC_DATE,'2001-11-11')
                   ,COALESCE(A.RREP_DATE,'2001-11-11')
                   ,COALESCE(A.IN_DATE,'2001-11-11')
                   ,A.CHQ_RCV_BK
                   ,A.CHQ_RCV_BR
                   ,COALESCE(A.CHQ_RET_NO,'')
                   ,COALESCE(A.CHQ_COR_REA,'')
                   ,COALESCE(A.CHQ_RET_TIME,0)
                   ,COALESCE(A.BCNO_STA,'')
                   ,A.CHQ_DATE
                   ,COALESCE(A.CHQ_BR_RCV,'')
                   ,COALESCE(A.BC_NO,'')
                   ,A.FINE_CHQ
            FROM    HOST_CHEQUE_DETAIL A
                    LEFT OUTER JOIN HOST_CUSTOM_SUPPOR B
                    ON B.BC_NO = A.BC_NO
      *             LEFT OUTER JOIN HOST_CUSTOM_DETAIL C
      *             ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_ACCOUNT_PROFILE C
                    ON C.AC_NO = A.AC_NO
                    LEFT OUTER JOIN HOST_GROUP_PROFILE D
                    ON D.GROUP_ID = C.GROUP_CODE
            WHERE   A.AC_NO = :AC-NO             AND
                    A.CREC_DATE BETWEEN :TRANS-FROM-DATE AND
                                        :TRANS-TO-DATE   AND
                    A.SORT_TYPE IN (1,2)         AND
                    A.BCNO_STA = 'CREC'
      * OP60030006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'P'
      * OP60030006 <--
      * RC60080006 -->
                    AND COALESCE(A.CQM_FLAG, 'N') <> 'Y'
      * RC60080006 <--
            ORDER BY CHQ_RCV_BR, CHQ_BK, CHQ_BR, CHQ_NO
           END-EXEC.
      *            ,A.TYPE_BCDAY
      *            ,A.TYPE_BCDAY
      *            ,A.TYPE_BCDAY
      *            ,A.TYPE_BCDAY
      *-------------------------------------------------------------*
       LINKAGE SECTION.
       01  DFHCOMMAREA                 PIC X(100).
      *=============================================================*   05560000
       PROCEDURE DIVISION.
      *=============================================================*   05560000
       000-INITIAL.
      * KOY
      *    MOVE TEST-SUMMARY                 TO INREC.
      *    MOVE TEST-DETAIL                  TO INREC.
           MOVE DFHCOMMAREA                  TO INREC.
           MOVE SPACES                       TO OUTREC.
           EXEC SQL WHENEVER SQLERROR   CONTINUE  END-EXEC.             05590000
           EXEC SQL WHENEVER SQLWARNING CONTINUE  END-EXEC.             05600000
           EXEC SQL WHENEVER NOT FOUND  CONTINUE  END-EXEC.             05610000
           EXEC CICS HANDLE CONDITION SYSIDERR(720-NO-SYSID)            05620000
                                      TERMERR(740-TERM-ERR)             05630000
                                      CBIDERR(8888-ERROR)               05640000
                                      INVREQ(7777-ERROR)                05640000
                                      SESSIONERR(8888-ERROR)            05640000
                                      EODS(8888-ERROR)                  05640000
                                      EOF(8888-ERROR)                   05640000
                                      IGREQCD(8888-ERROR)               05640000
                                      INBFMH(8888-ERROR)                05640000
                                      LENGERR(8888-ERROR)               05640000
                                      NOTALLOC(8888-ERROR)              05640000
                                      RDATT(8888-ERROR)                 05640000
                                      SIGNAL(8888-ERROR)                05640000
                                      ERROR(8888-ERROR) END-EXEC.       05640000
           EXEC  CICS HANDLE ABEND LABEL(8888-ERROR)    END-EXEC.       05620000

           MOVE  0                           TO IND-1.                  07751000
           MOVE  'N'                         TO END-DEL.                05820000
           MOVE  'Y'                         TO FIRST-FETCH.            05820000
           MOVE  0                           TO SERIAL-NOX.             05820000

           IF TRAN-ID NOT = '0001' AND TRAN-ID NOT = '0002'
              MOVE 'BCS'                     TO APPL-NAME
              MOVE 'E '                      TO STATUS-FLAG
              MOVE '00010'                   TO STATUS-NUMBER
              MOVE 'INVALID TRANSACTION ID.' TO STATUS-DESC
              MOVE '00000'                   TO SQL-STATE
              MOVE '0000000000'              TO SQL-CODE
              GO TO RTN-CLOSE.                                          05810000

           IF STATUS-CHQ NOT = '1' AND STATUS-CHQ NOT = '2' AND
              STATUS-CHQ NOT = '3' AND STATUS-CHQ NOT = '4' AND
              STATUS-CHQ NOT = '5'
              MOVE 'BCS'                     TO APPL-NAME
              MOVE 'E '                      TO STATUS-FLAG
              MOVE '00020'                   TO STATUS-NUMBER
              MOVE 'INVALID STATUS.'         TO STATUS-DESC
              MOVE '00000'                   TO SQL-STATE
              MOVE '0000000000'              TO SQL-CODE
              GO TO RTN-CLOSE.

           PERFORM 2000-PROCESS-RTN         THRU 2000-EXIT.
           PERFORM 9000-TERMINATE-RTN       THRU 9000-EXIT.

       000-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       1100-FETCH-SUMMARY-DTL.                                          ********
      *-------------------------------------------------------------*
           MOVE  SPACES                TO  WK-CHQDTL1.
           MOVE  0                     TO  WK-CHQDTL2.
           MOVE  SPACES                TO  DET-REC.

           EXEC SQL  FETCH SUMMARY                                      06010000
           INTO  :WSTAT-TYPE,
                 :WCHQ-BR-RCV,
                 :WSUM-ITEM
           END-EXEC.                                                    06100000

           IF SQLCODE  =  NOT-FOUND AND FIRST-FETCH = 'Y'               06110000
              MOVE  'Y'                        TO END-FETCH                  061
              MOVE 'BCS'                       TO APPL-NAME
              MOVE 'E '                        TO STATUS-FLAG
              MOVE '00200'                     TO STATUS-NUMBER
              MOVE 'NO DATA FOR YOUR INQUIRY.' TO STATUS-DESC
              MOVE SQLSTATE                    TO WK-SQL-STATE
              MOVE WK-SQL-STATE                TO SQL-STATE
              MOVE SQLCODE                     TO WK-SQL-CODE
              MOVE WK-SQL-CODE                 TO SQL-CODE
           ELSE
              IF SQLCODE  =  NOT-FOUND                                  06110000
                 MOVE  'Y'                     TO END-FETCH             06120000
                 MOVE 'BCS'                    TO APPL-NAME
                 MOVE 'S '                     TO STATUS-FLAG
                 MOVE '00000'                  TO STATUS-NUMBER
                 MOVE 'INQUIRY COMPLETE.'      TO STATUS-DESC
                 MOVE SQLSTATE                 TO WK-SQL-STATE
                 MOVE WK-SQL-STATE             TO SQL-STATE
                 MOVE SQLCODE                  TO WK-SQL-CODE
                 MOVE WK-SQL-CODE              TO SQL-CODE
                 PERFORM 4100-WRITE-SUMMARY    THRU  4100-EXIT
              ELSE                                                      06131000
                 IF SQLCODE  =  0                                       06131000
                    IF FIRST-FETCH = 'Y'
                       MOVE 'N'                   TO FIRST-FETCH
                       MOVE WCHQ-BR-RCV           TO WK-SAVE-BRANCH
                       PERFORM 2550-RCV-ITEM      THRU 2550-EXIT
                    ELSE
                       PERFORM 2550-RCV-ITEM      THRU 2550-EXIT
                 ELSE                                                        061
                    MOVE 'BCS'                     TO APPL-NAME
                    MOVE 'E '                      TO STATUS-FLAG
                    MOVE '00100'                   TO STATUS-NUMBER
                    MOVE 'FETCH CURSOR ERROR.'     TO STATUS-DESC
                    MOVE SQLSTATE                  TO WK-SQL-STATE
                    MOVE WK-SQL-STATE              TO SQL-STATE
                    MOVE SQLCODE                   TO WK-SQL-CODE
                    MOVE WK-SQL-CODE               TO SQL-CODE
                    PERFORM 9000-TERMINATE-RTN.                              061

       1100-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       1200-FETCH-DETSETUP-DTL.                                         ********
      *-------------------------------------------------------------*
           MOVE  0                     TO  IND-1 IND-2 IND-3.
           MOVE  SPACES                TO  WK-CHQDTL1.
           MOVE  0                     TO  WK-CHQDTL2.
           MOVE  SPACES                TO  DET-REC.

           EXEC SQL  FETCH DETSETUP                                     06010000
           INTO  :WCHQ-BR-SET,
                 :WCHQ-BK,
                 :WCHQ-BR,
                 :WCHQ-NO,
                 :WAC-NO,
                 :WAC-BR,
                 :WAC-NAME,
                 :WAC-GROUP,
                 :WAABC-IC-DDATE,
                 :WAABC-IC-EDATE,
                 :WAABC-CL-DDATE,
                 :WAABC-CL-EDATE,
                 :WAABC-ICBC-DDATE,
                 :WAABC-ICBC-EDATE,
                 :WAABC-OCBC-DDATE,
                 :WAABC-OCBC-EDATE,
                 :WGABC-IC-DDATE,
                 :WGABC-IC-EDATE,
                 :WGABC-CL-DDATE,
                 :WGABC-CL-EDATE,
                 :WGABC-ICBC-DDATE,
                 :WGABC-ICBC-EDATE,
                 :WGABC-OCBC-DDATE,
                 :WGABC-OCBC-EDATE,
      *          :WABC-BEF-EDATE,
      *          :WABC-BEF-DDATE,
      *          :WABC-COM,
                 :WCHQ-AMT,
                 :WCHQ-COM,
                 :WBEF-COM,
                 :WINVOICE,
                 :WCODE,
                 :WOTHER,
                 :WCHQ-REF-NO,
                 :WCHQ-TYPE,
                 :WCUST-DATE,
                 :WSEND-DATE,
                 :WCREC-DATE,
                 :WPAID-DATE,
                 :WRETC-DATE,
                 :WRREP-DATE,
                 :WIN-DATE,
                 :WCHQ-RCV-BK,
                 :WCHQ-RCV-BR,
                 :WCHQ-RET-NO,
                 :WCHQ-COR,
                 :WCHQ-RET-TIME,
                 :WCHQ-STAT,
                 :WCHQ-DATE,
                 :WCHQ-BR-RCV,
                 :WBC-NO,
                 :WFINE-CHQ
           END-EXEC.                                                    06100000
      *          :WTYPE-BCDAY,

           IF SQLCODE  =  NOT-FOUND AND FIRST-FETCH = 'Y'               06110000
              MOVE  'Y'                        TO END-FETCH                  061
              MOVE 'BCS'                       TO APPL-NAME
              MOVE 'E '                        TO STATUS-FLAG
              MOVE '00200'                     TO STATUS-NUMBER
              MOVE 'NO DATA FOR YOUR INQUIRY.' TO STATUS-DESC
              MOVE SQLSTATE                    TO WK-SQL-STATE
              MOVE WK-SQL-STATE                TO SQL-STATE
              MOVE SQLCODE                     TO WK-SQL-CODE
              MOVE WK-SQL-CODE                 TO SQL-CODE
           ELSE
              IF SQLCODE  =  NOT-FOUND                                  06110000
                 MOVE  'Y'                     TO END-FETCH             06120000
                 MOVE 'BCS'                    TO APPL-NAME
                 MOVE 'S '                     TO STATUS-FLAG
                 MOVE '00000'                  TO STATUS-NUMBER
                 MOVE 'INQUIRY COMPLETE.'      TO STATUS-DESC
                 MOVE SQLSTATE                 TO WK-SQL-STATE
                 MOVE WK-SQL-STATE             TO SQL-STATE
                 MOVE SQLCODE                  TO WK-SQL-CODE
                 MOVE WK-SQL-CODE              TO SQL-CODE
              ELSE                                                      06131000
                 IF SQLCODE  =  0                                       06131000
                    MOVE 'N'                       TO FIRST-FETCH
                    PERFORM 4200-WRITE-DETAIL    THRU 4200-EXIT
                 ELSE                                                        061
                    MOVE 'BCS'                     TO APPL-NAME
                    MOVE 'E '                      TO STATUS-FLAG
                    MOVE '00100'                   TO STATUS-NUMBER
                    MOVE 'FETCH CURSOR ERROR.'     TO STATUS-DESC
                    MOVE SQLSTATE                  TO WK-SQL-STATE
                    MOVE WK-SQL-STATE              TO SQL-STATE
                    MOVE SQLCODE                   TO WK-SQL-CODE
                    MOVE WK-SQL-CODE               TO SQL-CODE
                    PERFORM 9000-TERMINATE-RTN.                              061

       1200-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       1300-FETCH-DETPAID-DTL.                                          ********
      *-------------------------------------------------------------*
           MOVE  0                     TO  IND-1 IND-2 IND-3.
           MOVE  SPACES                TO  WK-CHQDTL1.
           MOVE  0                     TO  WK-CHQDTL2.
           MOVE  SPACES                TO  DET-REC.

           EXEC SQL  FETCH DETPAID                                      06010000
           INTO  :WCHQ-BR-SET,
                 :WCHQ-BK,
                 :WCHQ-BR,
                 :WCHQ-NO,
                 :WAC-NO,
                 :WAC-BR,
                 :WAC-NAME,
                 :WAC-GROUP,
                 :WAABC-IC-DDATE,
                 :WAABC-IC-EDATE,
                 :WAABC-CL-DDATE,
                 :WAABC-CL-EDATE,
                 :WAABC-ICBC-DDATE,
                 :WAABC-ICBC-EDATE,
                 :WAABC-OCBC-DDATE,
                 :WAABC-OCBC-EDATE,
                 :WGABC-IC-DDATE,
                 :WGABC-IC-EDATE,
                 :WGABC-CL-DDATE,
                 :WGABC-CL-EDATE,
                 :WGABC-ICBC-DDATE,
                 :WGABC-ICBC-EDATE,
                 :WGABC-OCBC-DDATE,
                 :WGABC-OCBC-EDATE,
      *          :WABC-BEF-EDATE,
      *          :WABC-BEF-DDATE,
      *          :WABC-COM,
                 :WCHQ-AMT,
                 :WCHQ-COM,
                 :WBEF-COM,
                 :WINVOICE,
                 :WCODE,
                 :WOTHER,
                 :WCHQ-REF-NO,
                 :WCHQ-TYPE,
                 :WCUST-DATE,
                 :WSEND-DATE,
                 :WCREC-DATE,
                 :WPAID-DATE,
                 :WRETC-DATE,
                 :WRREP-DATE,
                 :WIN-DATE,
                 :WCHQ-RCV-BK,
                 :WCHQ-RCV-BR,
                 :WCHQ-RET-NO,
                 :WCHQ-COR,
                 :WCHQ-RET-TIME,
                 :WCHQ-STAT,
                 :WCHQ-DATE,
                 :WCHQ-BR-RCV,
                 :WBC-NO,
                 :WFINE-CHQ
           END-EXEC.                                                    06100000
      *          :WTYPE-BCDAY,

           IF SQLCODE  =  NOT-FOUND AND FIRST-FETCH = 'Y'               06110000
              MOVE  'Y'                        TO END-FETCH                  061
              MOVE 'BCS'                       TO APPL-NAME
              MOVE 'E '                        TO STATUS-FLAG
              MOVE '00200'                     TO STATUS-NUMBER
              MOVE 'NO DATA FOR YOUR INQUIRY.' TO STATUS-DESC
              MOVE SQLSTATE                    TO WK-SQL-STATE
              MOVE WK-SQL-STATE                TO SQL-STATE
              MOVE SQLCODE                     TO WK-SQL-CODE
              MOVE WK-SQL-CODE                 TO SQL-CODE
           ELSE
              IF SQLCODE  =  NOT-FOUND                                  06110000
                 MOVE  'Y'                     TO END-FETCH             06120000
                 MOVE 'BCS'                    TO APPL-NAME
                 MOVE 'S '                     TO STATUS-FLAG
                 MOVE '00000'                  TO STATUS-NUMBER
                 MOVE 'INQUIRY COMPLETE.'      TO STATUS-DESC
                 MOVE SQLSTATE                 TO WK-SQL-STATE
                 MOVE WK-SQL-STATE             TO SQL-STATE
                 MOVE SQLCODE                  TO WK-SQL-CODE
                 MOVE WK-SQL-CODE              TO SQL-CODE
              ELSE                                                      06131000
                 IF SQLCODE  =  0                                       06131000
                    MOVE 'N'                       TO FIRST-FETCH
                    PERFORM 4200-WRITE-DETAIL    THRU 4200-EXIT
                 ELSE                                                        061
                    MOVE 'BCS'                     TO APPL-NAME
                    MOVE 'E '                      TO STATUS-FLAG
                    MOVE '00100'                   TO STATUS-NUMBER
                    MOVE 'FETCH CURSOR ERROR.'     TO STATUS-DESC
                    MOVE SQLSTATE                  TO WK-SQL-STATE
                    MOVE WK-SQL-STATE              TO SQL-STATE
                    MOVE SQLCODE                   TO WK-SQL-CODE
                    MOVE WK-SQL-CODE               TO SQL-CODE
                    PERFORM 9000-TERMINATE-RTN.                              061
       1300-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       1400-FETCH-DETRETURN-DTL.                                        ********
      *-------------------------------------------------------------*
           MOVE  0                     TO  IND-1 IND-2 IND-3.
           MOVE  SPACES                TO  WK-CHQDTL1.
           MOVE  0                     TO  WK-CHQDTL2.
           MOVE  SPACES                TO  DET-REC.

           EXEC SQL  FETCH DETRETURN                                    06010000
           INTO  :WCHQ-BR-SET,
                 :WCHQ-BK,
                 :WCHQ-BR,
                 :WCHQ-NO,
                 :WAC-NO,
                 :WAC-BR,
                 :WAC-NAME,
                 :WAC-GROUP,
                 :WAABC-IC-DDATE,
                 :WAABC-IC-EDATE,
                 :WAABC-CL-DDATE,
                 :WAABC-CL-EDATE,
                 :WAABC-ICBC-DDATE,
                 :WAABC-ICBC-EDATE,
                 :WAABC-OCBC-DDATE,
                 :WAABC-OCBC-EDATE,
                 :WGABC-IC-DDATE,
                 :WGABC-IC-EDATE,
                 :WGABC-CL-DDATE,
                 :WGABC-CL-EDATE,
                 :WGABC-ICBC-DDATE,
                 :WGABC-ICBC-EDATE,
                 :WGABC-OCBC-DDATE,
                 :WGABC-OCBC-EDATE,
      *          :WABC-BEF-EDATE,
      *          :WABC-BEF-DDATE,
      *          :WABC-COM,
                 :WCHQ-AMT,
                 :WCHQ-COM,
                 :WBEF-COM,
                 :WINVOICE,
                 :WCODE,
                 :WOTHER,
                 :WCHQ-REF-NO,
                 :WCHQ-TYPE,
                 :WCUST-DATE,
                 :WSEND-DATE,
                 :WCREC-DATE,
                 :WPAID-DATE,
                 :WRETC-DATE,
                 :WRREP-DATE,
                 :WIN-DATE,
                 :WCHQ-RCV-BK,
                 :WCHQ-RCV-BR,
                 :WCHQ-RET-NO,
                 :WCHQ-COR,
                 :WCHQ-RET-TIME,
                 :WCHQ-STAT,
                 :WCHQ-DATE,
                 :WCHQ-BR-RCV,
                 :WBC-NO,
                 :WFINE-CHQ
           END-EXEC.                                                    06100000
      *          :WTYPE-BCDAY,

           IF SQLCODE  =  NOT-FOUND AND FIRST-FETCH = 'Y'               06110000
              MOVE  'Y'                        TO END-FETCH                  061
              MOVE 'BCS'                       TO APPL-NAME
              MOVE 'E '                        TO STATUS-FLAG
              MOVE '00200'                     TO STATUS-NUMBER
              MOVE 'NO DATA FOR YOUR INQUIRY.' TO STATUS-DESC
              MOVE SQLSTATE                    TO WK-SQL-STATE
              MOVE WK-SQL-STATE                TO SQL-STATE
              MOVE SQLCODE                     TO WK-SQL-CODE
              MOVE WK-SQL-CODE                 TO SQL-CODE
           ELSE
              IF SQLCODE  =  NOT-FOUND                                  06110000
                 MOVE  'Y'                     TO END-FETCH             06120000
                 MOVE 'BCS'                    TO APPL-NAME
                 MOVE 'S '                     TO STATUS-FLAG
                 MOVE '00000'                  TO STATUS-NUMBER
                 MOVE 'INQUIRY COMPLETE.'      TO STATUS-DESC
                 MOVE SQLSTATE                 TO WK-SQL-STATE
                 MOVE WK-SQL-STATE             TO SQL-STATE
                 MOVE SQLCODE                  TO WK-SQL-CODE
                 MOVE WK-SQL-CODE              TO SQL-CODE
              ELSE                                                      06131000
                 IF SQLCODE  =  0                                       06131000
                    MOVE 'N'                       TO FIRST-FETCH
                    PERFORM 4200-WRITE-DETAIL    THRU 4200-EXIT
                 ELSE                                                        061
                    MOVE 'BCS'                     TO APPL-NAME
                    MOVE 'E '                      TO STATUS-FLAG
                    MOVE '00100'                   TO STATUS-NUMBER
                    MOVE 'FETCH CURSOR ERROR.'     TO STATUS-DESC
                    MOVE SQLSTATE                  TO WK-SQL-STATE
                    MOVE WK-SQL-STATE              TO SQL-STATE
                    MOVE SQLCODE                   TO WK-SQL-CODE
                    MOVE WK-SQL-CODE               TO SQL-CODE
                    PERFORM 9000-TERMINATE-RTN.                              061

       1400-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       1500-FETCH-DETALLTRN-DTL.                                        ********
      *-------------------------------------------------------------*
           MOVE  0                     TO  IND-1 IND-2 IND-3.
           MOVE  SPACES                TO  WK-CHQDTL1.
           MOVE  0                     TO  WK-CHQDTL2.
           MOVE  SPACES                TO  DET-REC.

           EXEC SQL  FETCH DETALLTRN                                    06010000
           INTO  :WCHQ-BR-SET,
                 :WCHQ-BK,
                 :WCHQ-BR,
                 :WCHQ-NO,
                 :WAC-NO,
                 :WAC-BR,
                 :WAC-NAME,
                 :WAC-GROUP,
                 :WAABC-IC-DDATE,
                 :WAABC-IC-EDATE,
                 :WAABC-CL-DDATE,
                 :WAABC-CL-EDATE,
                 :WAABC-ICBC-DDATE,
                 :WAABC-ICBC-EDATE,
                 :WAABC-OCBC-DDATE,
                 :WAABC-OCBC-EDATE,
                 :WGABC-IC-DDATE,
                 :WGABC-IC-EDATE,
                 :WGABC-CL-DDATE,
                 :WGABC-CL-EDATE,
                 :WGABC-ICBC-DDATE,
                 :WGABC-ICBC-EDATE,
                 :WGABC-OCBC-DDATE,
                 :WGABC-OCBC-EDATE,
      *          :WABC-BEF-EDATE,
      *          :WABC-BEF-DDATE,
      *          :WABC-COM,
                 :WCHQ-AMT,
                 :WCHQ-COM,
                 :WBEF-COM,
                 :WINVOICE,
                 :WCODE,
                 :WOTHER,
                 :WCHQ-REF-NO,
                 :WCHQ-TYPE,
                 :WCUST-DATE,
                 :WSEND-DATE,
                 :WCREC-DATE,
                 :WPAID-DATE,
                 :WRETC-DATE,
                 :WRREP-DATE,
                 :WIN-DATE,
                 :WCHQ-RCV-BK,
                 :WCHQ-RCV-BR,
                 :WCHQ-RET-NO,
                 :WCHQ-COR,
                 :WCHQ-RET-TIME,
                 :WCHQ-STAT,
                 :WCHQ-DATE,
                 :WCHQ-BR-RCV,
                 :WBC-NO,
                 :WFINE-CHQ
           END-EXEC.                                                    06100000
      *          :WTYPE-BCDAY,

           IF SQLCODE  =  NOT-FOUND AND FIRST-FETCH = 'Y'               06110000
              MOVE  'Y'                        TO END-FETCH                  061
              MOVE 'BCS'                       TO APPL-NAME
              MOVE 'E '                        TO STATUS-FLAG
              MOVE '00200'                     TO STATUS-NUMBER
              MOVE 'NO DATA FOR YOUR INQUIRY.' TO STATUS-DESC
              MOVE SQLSTATE                    TO WK-SQL-STATE
              MOVE WK-SQL-STATE                TO SQL-STATE
              MOVE SQLCODE                     TO WK-SQL-CODE
              MOVE WK-SQL-CODE                 TO SQL-CODE
           ELSE
              IF SQLCODE  =  NOT-FOUND                                  06110000
                 MOVE  'Y'                     TO END-FETCH             06120000
                 MOVE 'BCS'                    TO APPL-NAME
                 MOVE 'S '                     TO STATUS-FLAG
                 MOVE '00000'                  TO STATUS-NUMBER
                 MOVE 'INQUIRY COMPLETE.'      TO STATUS-DESC
                 MOVE SQLSTATE                 TO WK-SQL-STATE
                 MOVE WK-SQL-STATE             TO SQL-STATE
                 MOVE SQLCODE                  TO WK-SQL-CODE
                 MOVE WK-SQL-CODE              TO SQL-CODE
              ELSE                                                      06131000
                 IF SQLCODE  =  0                                       06131000
                    MOVE 'N'                       TO FIRST-FETCH
                    PERFORM 4200-WRITE-DETAIL    THRU 4200-EXIT
                 ELSE                                                        061
                    MOVE 'BCS'                     TO APPL-NAME
                    MOVE 'E '                      TO STATUS-FLAG
                    MOVE '00100'                   TO STATUS-NUMBER
                    MOVE 'FETCH CURSOR ERROR.'     TO STATUS-DESC
                    MOVE SQLSTATE                  TO WK-SQL-STATE
                    MOVE WK-SQL-STATE              TO SQL-STATE
                    MOVE SQLCODE                   TO WK-SQL-CODE
                    MOVE WK-SQL-CODE               TO SQL-CODE
                    PERFORM 9000-TERMINATE-RTN.                              061

       1500-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       1550-FETCH-DETALLDWN-DTL.                                        ********
      *-------------------------------------------------------------*
           MOVE  0                     TO  IND-1 IND-2 IND-3.
           MOVE  SPACES                TO  WK-CHQDTL1.
           MOVE  0                     TO  WK-CHQDTL2.
           MOVE  SPACES                TO  DET-REC.

           EXEC SQL  FETCH DETALLDWN                                    06010000
           INTO  :WCHQ-BR-SET,
                 :WCHQ-BK,
                 :WCHQ-BR,
                 :WCHQ-NO,
                 :WAC-NO,
                 :WAC-BR,
                 :WAC-NAME,
                 :WAC-GROUP,
                 :WAABC-IC-DDATE,
                 :WAABC-IC-EDATE,
                 :WAABC-CL-DDATE,
                 :WAABC-CL-EDATE,
                 :WAABC-ICBC-DDATE,
                 :WAABC-ICBC-EDATE,
                 :WAABC-OCBC-DDATE,
                 :WAABC-OCBC-EDATE,
                 :WGABC-IC-DDATE,
                 :WGABC-IC-EDATE,
                 :WGABC-CL-DDATE,
                 :WGABC-CL-EDATE,
                 :WGABC-ICBC-DDATE,
                 :WGABC-ICBC-EDATE,
                 :WGABC-OCBC-DDATE,
                 :WGABC-OCBC-EDATE,
      *          :WABC-BEF-EDATE,
      *          :WABC-BEF-DDATE,
      *          :WABC-COM,
                 :WCHQ-AMT,
                 :WCHQ-COM,
                 :WBEF-COM,
                 :WINVOICE,
                 :WCODE,
                 :WOTHER,
                 :WCHQ-REF-NO,
                 :WCHQ-TYPE,
                 :WCUST-DATE,
                 :WSEND-DATE,
                 :WCREC-DATE,
                 :WPAID-DATE,
                 :WRETC-DATE,
                 :WRREP-DATE,
                 :WIN-DATE,
                 :WCHQ-RCV-BK,
                 :WCHQ-RCV-BR,
                 :WCHQ-RET-NO,
                 :WCHQ-COR,
                 :WCHQ-RET-TIME,
                 :WCHQ-STAT,
                 :WCHQ-DATE,
                 :WCHQ-BR-RCV,
                 :WBC-NO,
                 :WFINE-CHQ
           END-EXEC.                                                    06100000
      *          :WTYPE-BCDAY,

           IF SQLCODE  =  NOT-FOUND AND FIRST-FETCH = 'Y'               06110000
              MOVE  'Y'                        TO END-FETCH                  061
              MOVE 'BCS'                       TO APPL-NAME
              MOVE 'E '                        TO STATUS-FLAG
              MOVE '00200'                     TO STATUS-NUMBER
              MOVE 'NO DATA FOR YOUR INQUIRY.' TO STATUS-DESC
              MOVE SQLSTATE                    TO WK-SQL-STATE
              MOVE WK-SQL-STATE                TO SQL-STATE
              MOVE SQLCODE                     TO WK-SQL-CODE
              MOVE WK-SQL-CODE                 TO SQL-CODE
           ELSE
              IF SQLCODE  =  NOT-FOUND                                  06110000
                 MOVE  'Y'                     TO END-FETCH             06120000
                 MOVE 'BCS'                    TO APPL-NAME
                 MOVE 'S '                     TO STATUS-FLAG
                 MOVE '00000'                  TO STATUS-NUMBER
                 MOVE 'INQUIRY COMPLETE.'      TO STATUS-DESC
                 MOVE SQLSTATE                 TO WK-SQL-STATE
                 MOVE WK-SQL-STATE             TO SQL-STATE
                 MOVE SQLCODE                  TO WK-SQL-CODE
                 MOVE WK-SQL-CODE              TO SQL-CODE
              ELSE                                                      06131000
                 IF SQLCODE  =  0                                       06131000
                    MOVE 'N'                       TO FIRST-FETCH
                    PERFORM 4200-WRITE-DETAIL    THRU 4200-EXIT
                 ELSE                                                        061
                    MOVE 'BCS'                     TO APPL-NAME
                    MOVE 'E '                      TO STATUS-FLAG
                    MOVE '00100'                   TO STATUS-NUMBER
                    MOVE 'FETCH CURSOR ERROR.'     TO STATUS-DESC
                    MOVE SQLSTATE                  TO WK-SQL-STATE
                    MOVE WK-SQL-STATE              TO SQL-STATE
                    MOVE SQLCODE                   TO WK-SQL-CODE
                    MOVE WK-SQL-CODE               TO SQL-CODE
                    PERFORM 9000-TERMINATE-RTN.                              061

       1550-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2000-PROCESS-RTN.                                                ********
      *-------------------------------------------------------------*
           PERFORM 3000-DEL-CHQDTL          THRU 3000-EXIT
                   UNTIL END-DEL = 'Y'.

           IF TRAN-ID = '0001' THEN
              PERFORM 2500-SUMMARY-RTN      THRU 2500-EXIT.

           IF TRAN-ID = '0002' AND STATUS-CHQ = '1' THEN
              PERFORM 2600-CHQSETUP-RTN     THRU 2600-EXIT.

           IF TRAN-ID = '0002' AND STATUS-CHQ = '2' THEN
              PERFORM 2700-CHQPAID-RTN      THRU 2700-EXIT.

           IF TRAN-ID = '0002' AND STATUS-CHQ = '3' THEN
              PERFORM 2800-CHQRETURN-RTN    THRU 2800-EXIT.

           IF TRAN-ID = '0002' AND STATUS-CHQ = '4' THEN
              PERFORM 2900-CHQALLTRN-RTN    THRU 2900-EXIT.

           IF TRAN-ID = '0002' AND STATUS-CHQ = '5' THEN
              PERFORM 2950-CHQALLDWN-RTN    THRU 2950-EXIT.

       2000-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2500-SUMMARY-RTN.                                                ********
      *-------------------------------------------------------------*
           MOVE 0                           TO WK-SUM-SETUP.
           MOVE 0                           TO WK-SUM-PAID.
           MOVE 0                           TO WK-SUM-RETURN.

           EXEC SQL OPEN  SUMMARY  END-EXEC.                            05670000
                                                                        05960000
           IF  SQLCODE  NOT = 0                                         05750000
               MOVE 'BCS'                     TO APPL-NAME
               MOVE 'E '                      TO STATUS-FLAG
               MOVE '00300'                   TO STATUS-NUMBER
               MOVE 'OPEN SQL ERROR.'         TO STATUS-DESC
               MOVE SQLSTATE                  TO WK-SQL-STATE
               MOVE WK-SQL-STATE              TO SQL-STATE
               MOVE SQLCODE                   TO WK-SQL-CODE
               MOVE WK-SQL-CODE               TO SQL-CODE
               GO TO RTN-CLOSE.

           MOVE  'N'                        TO END-FETCH.               05820000
           MOVE  ZERO                       TO WK-KREC.                 05820000
           MOVE  ZERO                       TO WK-DREC.                 05820000

           PERFORM 1100-FETCH-SUMMARY-DTL   THRU  1100-EXIT             05830000
                   UNTIL END-FETCH  = 'Y'.                              05840000

       2500-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2550-RCV-ITEM.                                                   ********
      *-------------------------------------------------------------*
           IF WK-SAVE-BRANCH NOT = WCHQ-BR-RCV
              PERFORM 4100-WRITE-SUMMARY      THRU  4100-EXIT
              MOVE WCHQ-BR-RCV                TO WK-SAVE-BRANCH
              MOVE 0                          TO WK-SUM-SETUP
              MOVE 0                          TO WK-SUM-PAID
              MOVE 0                          TO WK-SUM-RETURN.

           IF WSTAT-TYPE = '1'
              MOVE WSUM-ITEM                  TO WK-SUM-SETUP
           ELSE
              IF WSTAT-TYPE = '2'
                 MOVE WSUM-ITEM               TO WK-SUM-PAID
              ELSE
                 IF WSTAT-TYPE = '3'
                    MOVE WSUM-ITEM            TO WK-SUM-RETURN
                 ELSE
                    NEXT SENTENCE.

       2550-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2600-CHQSETUP-RTN.                                               ********
      *-------------------------------------------------------------*
           EXEC SQL OPEN  DETSETUP  END-EXEC.                           05670000
                                                                        05960000
           IF  SQLCODE  NOT = 0                                         05750000
               MOVE 'BCS'                     TO APPL-NAME
               MOVE 'E '                      TO STATUS-FLAG
               MOVE '00300'                   TO STATUS-NUMBER
               MOVE 'OPEN SQL ERROR.'         TO STATUS-DESC
               MOVE SQLSTATE                  TO WK-SQL-STATE
               MOVE WK-SQL-STATE              TO SQL-STATE
               MOVE SQLCODE                   TO WK-SQL-CODE
               MOVE WK-SQL-CODE               TO SQL-CODE
               GO TO RTN-CLOSE.

           MOVE  'N'                        TO END-FETCH.               05820000
           MOVE  ZERO                       TO WK-KREC.                 05820000
           MOVE  ZERO                       TO WK-DREC.                 05820000

           PERFORM 1200-FETCH-DETSETUP-DTL  THRU  1200-EXIT             05830000
                   UNTIL END-FETCH  = 'Y'.                              05840000

       2600-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2700-CHQPAID-RTN.                                                ********
      *-------------------------------------------------------------*
           EXEC SQL OPEN  DETPAID  END-EXEC.                            05670000
                                                                        05960000
           IF  SQLCODE  NOT = 0                                         05750000
               MOVE 'BCS'                     TO APPL-NAME
               MOVE 'E '                      TO STATUS-FLAG
               MOVE '00300'                   TO STATUS-NUMBER
               MOVE 'OPEN SQL ERROR.'         TO STATUS-DESC
               MOVE SQLSTATE                  TO WK-SQL-STATE
               MOVE WK-SQL-STATE              TO SQL-STATE
               MOVE SQLCODE                   TO WK-SQL-CODE
               MOVE WK-SQL-CODE               TO SQL-CODE
               GO TO RTN-CLOSE.

           MOVE  'N'                        TO END-FETCH.               05820000
           MOVE  ZERO                       TO WK-KREC.                 05820000
           MOVE  ZERO                       TO WK-DREC.                 05820000

           PERFORM 1300-FETCH-DETPAID-DTL   THRU  1300-EXIT             05830000
                   UNTIL END-FETCH  = 'Y'.                              05840000

       2700-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2800-CHQRETURN-RTN.                                              ********
      *-------------------------------------------------------------*
           EXEC SQL OPEN  DETRETURN  END-EXEC.                          05670000
                                                                        05960000
           IF  SQLCODE  NOT = 0                                         05750000
               MOVE 'BCS'                     TO APPL-NAME
               MOVE 'E '                      TO STATUS-FLAG
               MOVE '00300'                   TO STATUS-NUMBER
               MOVE 'OPEN SQL ERROR.'         TO STATUS-DESC
               MOVE SQLSTATE                  TO WK-SQL-STATE
               MOVE WK-SQL-STATE              TO SQL-STATE
               MOVE SQLCODE                   TO WK-SQL-CODE
               MOVE WK-SQL-CODE               TO SQL-CODE
               GO TO RTN-CLOSE.

           MOVE  'N'                        TO END-FETCH.               05820000
           MOVE  ZERO                       TO WK-KREC.                 05820000
           MOVE  ZERO                       TO WK-DREC.                 05820000

           PERFORM 1400-FETCH-DETRETURN-DTL   THRU  1400-EXIT           05830000
                   UNTIL END-FETCH  = 'Y'.                              05840000

       2800-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2900-CHQALLTRN-RTN.                                              ********
      *-------------------------------------------------------------*
           EXEC SQL OPEN  DETALLTRN  END-EXEC.                          05670000
                                                                        05960000
           IF  SQLCODE  NOT = 0                                         05750000
               MOVE 'BCS'                     TO APPL-NAME
               MOVE 'E '                      TO STATUS-FLAG
               MOVE '00300'                   TO STATUS-NUMBER
               MOVE 'OPEN SQL ERROR.'         TO STATUS-DESC
               MOVE SQLSTATE                  TO WK-SQL-STATE
               MOVE WK-SQL-STATE              TO SQL-STATE
               MOVE SQLCODE                   TO WK-SQL-CODE
               MOVE WK-SQL-CODE               TO SQL-CODE
               GO TO RTN-CLOSE.

           MOVE  'N'                        TO END-FETCH.               05820000
           MOVE  ZERO                       TO WK-KREC.                 05820000
           MOVE  ZERO                       TO WK-DREC.                 05820000

           PERFORM 1500-FETCH-DETALLTRN-DTL   THRU  1500-EXIT           05830000
                   UNTIL END-FETCH  = 'Y'.                              05840000

       2900-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       2950-CHQALLDWN-RTN.                                              ********
      *-------------------------------------------------------------*
           EXEC SQL OPEN  DETALLDWN  END-EXEC.                          05670000
                                                                        05960000
           IF  SQLCODE  NOT = 0                                         05750000
               MOVE 'BCS'                     TO APPL-NAME
               MOVE 'E '                      TO STATUS-FLAG
               MOVE '00300'                   TO STATUS-NUMBER
               MOVE 'OPEN SQL ERROR.'         TO STATUS-DESC
               MOVE SQLSTATE                  TO WK-SQL-STATE
               MOVE WK-SQL-STATE              TO SQL-STATE
               MOVE SQLCODE                   TO WK-SQL-CODE
               MOVE WK-SQL-CODE               TO SQL-CODE
               GO TO RTN-CLOSE.

           MOVE  'N'                        TO END-FETCH.               05820000
           MOVE  ZERO                       TO WK-KREC.                 05820000
           MOVE  ZERO                       TO WK-DREC.                 05820000

           PERFORM 1550-FETCH-DETALLDWN-DTL   THRU  1550-EXIT           05830000
                   UNTIL END-FETCH  = 'Y'.                              05840000

       2950-EXIT.
           EXIT.
      *-------------------------------------------------------------*
       3000-DEL-CHQDTL.                                                 ********
      *-------------------------------------------------------------*
           MOVE TRAN-ID                     TO DTRAN-ID.
           MOVE AC-NO                       TO DACCT-NO.
           MOVE TRANS-DATE                  TO TMP-DATE.
           MOVE TMP-YYYY                    TO WK-YYYY.
           MOVE TMP-MM                      TO WK-MM.
           MOVE TMP-DD                      TO WK-DD.
           MOVE WK-TRAN-DATE                TO DTRANS-DATE.
           ADD  1                           TO SERIAL-NOX.
           MOVE SERIAL-NOX                  TO DSERIAL-NO.

           EXEC  CICS HANDLE CONDITION
                             NOTFND(0010-NOTFND)
                             ENDFILE(0010-NOTFND)
                             INVREQ(0010-NOTFND)
                             ERROR(0010-NOTFND)
           END-EXEC.

           EXEC  CICS DELETE DATASET('BCBCMDTL')
                             RIDFLD(DET-KEY)
           END-EXEC.
           GO TO 3000-EXIT.

       0010-NOTFND.                                                     ********
           MOVE 'Y'                         TO END-DEL.

       3000-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       4100-WRITE-SUMMARY.
      *-------------------------------------------------------------*
           MOVE TRAN-ID                     TO DTRAN-ID.
           MOVE AC-NO                       TO DACCT-NO.
           MOVE TRANS-DATE                  TO TMP-DATE.
           MOVE TMP-YYYY                    TO WK-YYYY.
           MOVE TMP-MM                      TO WK-MM.
           MOVE TMP-DD                      TO WK-DD.
           MOVE WK-TRAN-DATE                TO DTRANS-DATE.
           ADD  1                           TO WK-KREC.
           MOVE WK-KREC                     TO DSERIAL-NO.
           MOVE WK-SAVE-BRANCH              TO DSETUP-BRANCH.
           MOVE WK-SUM-SETUP                TO DSETUP-ITEM.
           MOVE WK-SUM-PAID                 TO DPAID-ITEM.
           MOVE WK-SUM-RETURN               TO DRETURN-ITEM.

           EXEC  CICS  WRITE  DATASET('BCBCMDTL')
                              RIDFLD(DET-KEY)
                              LENGTH(WK-RECL)
                              FROM(DET-REC)
           END-EXEC.

       4100-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       4200-WRITE-DETAIL.
      *-------------------------------------------------------------*
           MOVE TRAN-ID                     TO DTRAN-ID.
           MOVE AC-NO                       TO DACCT-NO.
           MOVE AC-NO                       TO DAC-NO.
           MOVE TRANS-DATE                  TO TMP-DATE.
           MOVE TMP-YYYY                    TO WK-YYYY.
           MOVE TMP-MM                      TO WK-MM.
           MOVE TMP-DD                      TO WK-DD.
           MOVE WK-TRAN-DATE                TO DTRANS-DATE.
           MOVE TMP-DATE                    TO WDATE.
           MOVE W1-YY2                      TO W2-YY.
           MOVE W1-MM                       TO W2-MM.
           MOVE '/'                         TO W2-F1.
           MOVE '/'                         TO W2-F2.
           MOVE W1-DD                       TO W2-DD.
           MOVE W2-DATE                     TO DTRN-DATE.
      *    ADD  1                           TO WK-KREC.
      *    MOVE WK-KREC                     TO DSERIAL-NO.
           MOVE WCHQ-BR-SET                 TO DCHQ-BR-SET.
           MOVE WCHQ-BK                     TO WK-CHQ-BK.
           MOVE WCHQ-BK                     TO DCHQ-BK3.
           MOVE XX-CHQ-BK2                  TO DCHQ-BK.
           MOVE WCHQ-BR                     TO DCHQ-BR.
           MOVE WCHQ-NO                     TO WK-CHQ-NO.
           MOVE WCHQ-NO                     TO DCHQ-NO8.
           MOVE XX-CHQ-NO2                  TO DCHQ-NO.
           MOVE WAC-NAME                    TO DAC-NAME.
           MOVE WCHQ-AMT                    TO DCHQ-AMT.
      *    MOVE WCHQ-COM                    TO DCHQ-COM.
           MOVE WCHQ-DATE                   TO WDATE.
           MOVE W1-YY2                      TO W2-YY.
           MOVE W1-MM                       TO W2-MM.
           MOVE W1-DD                       TO W2-DD.
           MOVE '/'                         TO W2-F1.
           MOVE '/'                         TO W2-F2.
           MOVE W2-DATE                     TO DCHQ-DATE.
           MOVE WINVOICE                    TO TMP-INVOICE.
           MOVE WK-INVOICE                  TO DINVOICE.
           MOVE WSEND-DATE                  TO WDATE.
           MOVE W1-YY2                      TO W2-YY.
           MOVE W1-MM                       TO W2-MM.
           MOVE W1-DD                       TO W2-DD.
           MOVE '/'                         TO W2-F1.
           MOVE '/'                         TO W2-F2.
           MOVE W2-DATE                     TO DSEND-DATE.
           MOVE WCHQ-RCV-BK                 TO WK-CHQ-RCV-BK.
           MOVE WCHQ-RCV-BK                 TO DCHQ-R-BK3.
           MOVE XX-CHQ-RCV-BK2              TO DCHQ-R-BK.
           MOVE WCHQ-RCV-BR                 TO DCHQ-R-BR.
           MOVE WCHQ-RET-NO                 TO DCHQ-RET-NO.
           MOVE WCHQ-RET-TIME               TO DCHQ-RET-TIME.
      *W/C
      *    MOVE WCHQ-STAT                   TO DCHQ-STAT.
           IF (WFINE-CHQ  = SPACE) OR
              (WFINE-CHQ  = ' ')
               MOVE  '0'                    TO DCHQ-STAT
           ELSE
               MOVE WFINE-CHQ               TO DCHQ-STAT.
      * OP64030001 -->
      *    PERFORM  4210-READ-MATCH-BKBR  THRU 4210-EXIT.
           EXEC SQL
                CALL BCSGTYPC(:WCHQ-BR-RCV,
                              :WCHQ-BK,
                              :WCHQ-BR,
                              :TYPE-BCDAY)
           END-EXEC.
      * OP64030001 <--
           MOVE TYPE-BCDAY                  TO DCOL-TYPE.
           MOVE 'SCB'                       TO DSCB-BK-CD.
           MOVE WCHQ-BR-RCV                 TO DRCV-BR.
      *    MOVE WBC-NO                      TO DBC-NO.
           PERFORM 4300-MOVE-DETAIL1   THRU  4300-EXIT.
           PERFORM 5000-MOVE-DETAIL2   THRU  5000-EXIT.

      *    EXEC  CICS  WRITE  DATASET('BCBCMDTL')
      *                       RIDFLD(DET-KEY)
      *                       LENGTH(WK-RECL)
      *                       FROM(DET-REC)
      *    END-EXEC.

       4200-EXIT.                                                       ********
            EXIT.
      * OP64030001 -->
      *-------------------------------------------------------------*
      *4210-READ-MATCH-BKBR.
      *-------------------------------------------------------------*
      *--- READ MATCH BKBR -----*
      **   MOVE SPACE TO WORK-INF7.
      **   EXEC  SQL
      **       SELECT BOTCLCODE
      **       INTO :W-BOTCLCODE-1
      **       FROM  BCSXBKBR
      **       WHERE BKCODE = :WCHQ-BK AND BRCODE = :WCHQ-BR
      **   END-EXEC.
      *    EXEC  SQL
      *        SELECT CLEARING_HOUSE_CODE_NO1
      *        INTO :W-BOTCLCODE-1
      *        FROM  HOST_BKBR_ZONE
      *        WHERE BANK_CODE = :WCHQ-BK AND BRANCH_CODE = :WCHQ-BR
      *    END-EXEC.
      *    MOVE SQLCODE  TO SQL-CODE.
      *    IF  (SQLCODE NOT =  0 AND SQLCODE NOT = 100)
      *        DISPLAY 'ERROR CODE ACCOUNT NAME : ' SQL-CODE.
      *    IF  SQLCODE    =  NOT-FOUND
      *        MOVE   SPACE          TO  W-BOTCLCODE-1.
      **       MOVE   SPACE          TO  W-CHQ-RCV-BR.
      **   EXEC  SQL
      **       SELECT BOTCLCODE
      **       INTO :W-BOTCLCODE-2
      **       FROM  BCSXBKBR
      **       WHERE BKCODE = :WCHQ-RCV-BK AND BRCODE = :WCHQ-BR-RCV
      **   END-EXEC.
      *    EXEC  SQL
      *        SELECT CLEARING_HOUSE_CODE_NO1
      *        INTO :W-BOTCLCODE-2
      *        FROM  HOST_BKBR_ZONE
      *        WHERE BANK_CODE = :WCHQ-RCV-BK
      *          AND BRANCH_CODE = :WCHQ-BR-RCV
      *    END-EXEC.
      *    MOVE SQLCODE  TO SQL-CODE.
      *    IF  (SQLCODE NOT =  0 AND SQLCODE NOT = 100)
      *        DISPLAY 'ERROR CODE ACCOUNT NAME : ' SQL-CODE.
      *    IF  SQLCODE    =  NOT-FOUND
      *        MOVE   SPACE          TO  W-BOTCLCODE-2.
      *    IF  W-BOTCLCODE-1 = W-BOTCLCODE-2
      *        MOVE 'CL'  TO  TYPE-BCDAY
      *    ELSE
      *        MOVE 'BC'  TO  TYPE-BCDAY.
      *4210-EXIT.                                                       ********
      *     EXIT.
      * OP64030001 <--
      *-------------------------------------------------------------*
       4300-MOVE-DETAIL1.
      *-------------------------------------------------------------*
      *--- READ BANK AND BRANCH CHEQUE NAME -----*
           EXEC  SQL
             SELECT A.BK_NAME_OTH, COALESCE(B.BRANCH_NAME_THAI,' ')
               INTO :DCHQ-BK-NM,
                    :DCHQ-BR-NM
               FROM HOST_BANK_OTHER  A,
                    HOST_BKBR_ZONE  B
              WHERE A.BK_CODE_OTH = :WCHQ-BK
               AND (B.BANK_CODE = :WCHQ-BK
                AND B.BRANCH_CODE = :WCHQ-BR)
           END-EXEC.
      *    IF  WCHQ-BK = '014'
      *        EXEC  SQL
      *          SELECT A.BK_NAME_OTH, B.BR_NAME
      *            INTO :DCHQ-BK-NM,
      *                 :DCHQ-BR-NM
      *            FROM  HOST_BANK_OTHER A,
      *                  HOST_BRANCH_DETAIL B
      *           WHERE A.BK_CODE_OTH = :WCHQ-BK
      *             AND B.BR_CODE = :WCHQ-BR
      *        END-EXEC
      *    ELSE
      *        EXEC  SQL
      *          SELECT A.BK_NAME_OTH, B.BR_NAME_OTH
      *            INTO :DCHQ-BK-NM,
      *                 :DCHQ-BR-NM
      *            FROM HOST_BANK_OTHER  A,
      *                 HOST_BRANCH_OTHER  B
      *           WHERE A.BK_CODE_OTH = :WCHQ-BK
      *            AND (B.BK_CODE_OTH = :WCHQ-BK
      *             AND B.BR_CODE_OTH = :WCHQ-BR)
      *        END-EXEC.
      *--- READ CHEQUE RECEIVE BRANCH NAME -----*
                EXEC  SQL
                SELECT BR_NAME
                    INTO :DCHQ-R-BRNM
                    FROM  HOST_BRANCH_DETAIL
      *             WHERE BR_CODE = :WCHQ-BR
                    WHERE BR_CODE = :WCHQ-RCV-BR
                END-EXEC.
                IF  SQLCODE    =  NOT-FOUND
                    MOVE   SPACES   TO   DCHQ-R-BRNM.

      *--- READ SET BRANCH NAME -----*  W/C
                EXEC  SQL
                SELECT BR_NAME
                    INTO :DSET-BR-NM
                    FROM  HOST_BRANCH_DETAIL
                    WHERE BR_CODE = :WCHQ-BR-SET
                END-EXEC.
                IF  SQLCODE    =  NOT-FOUND
                    MOVE   SPACES   TO   DSET-BR-NM.

      *--- READ BCHQRV NAME -----*
                EXEC  SQL
                SELECT BR_NAME
                    INTO :DRCV-BR-NM
                    FROM  HOST_BRANCH_DETAIL
                    WHERE BR_CODE = :WCHQ-BR-RCV
                END-EXEC.
                IF  SQLCODE    =  NOT-FOUND
                    MOVE   SPACES   TO   DRCV-BR-NM.
      *    IF (WCHQ-RET-NO = '3') OR (WCHQ-RET-NO = '03')
      *    OR (WCHQ-RET-NO = '14')
      *    OR (WCHQ-RET-NO = '99')
      *       MOVE 0        TO DCHQ-COM
      *       MOVE 0        TO WTMP-COM
      *    ELSE
      *       MOVE WCHQ-COM TO DCHQ-COM
      *       MOVE WCHQ-COM TO WTMP-COM.

           MOVE WCHQ-COM TO DCHQ-COM.
           MOVE WCHQ-COM TO WTMP-COM.
           COMPUTE WCHQ-NET = WCHQ-AMT - WTMP-COM.
           MOVE  WCHQ-NET              TO  DCHQ-NET.
           MOVE  WCHQ-REF-NO           TO  DCHQ-REF-NO.
           MOVE  WCODE                 TO  DCODE.
           MOVE  WOTHER                TO  DOTHER.
           MOVE  WCHQ-TYPE             TO  DCHQ-TYPE.
           IF WCUST-DATE = SPACE
              MOVE  WSEND-DATE    TO   WDATE
           ELSE
              MOVE  WCUST-DATE    TO   WDATE.
           MOVE  W1-YY2                TO  W2-YY
           MOVE  W1-MM                 TO  W2-MM
           MOVE  W1-DD                 TO  W2-DD
           MOVE '/'                    TO  W2-F1
           MOVE '/'                    TO  W2-F2
           MOVE  W2-DATE               TO  DBCS-DATE.
           PERFORM 4400-MOVE-STATUS    THRU  4400-EXIT.
           PERFORM 4500-CHECK-CODE     THRU  4500-EXIT.
       4300-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       4400-MOVE-STATUS.
      *-------------------------------------------------------------*
           MOVE SPACE    TO ASTATUS.
           MOVE SPACE    TO DSTATUS.
           MOVE SPACE    TO OSTATUS.
           MOVE SPACE    TO SSTATUS.
           MOVE SPACE    TO CSTATUS.
           MOVE SPACE    TO PSTATUS.
           MOVE SPACE    TO RSTATUS.
           IF  WSEND-DATE = TRANS-DATE
               MOVE 'S'  TO SSTATUS.
           IF  WCREC-DATE = TRANS-DATE
               MOVE 'C'  TO CSTATUS.
           IF  WPAID-DATE = TRANS-DATE
               MOVE 'P'  TO PSTATUS.
           IF (WRETC-DATE = TRANS-DATE OR WRREP-DATE = TRANS-DATE)
               MOVE 'R'  TO RSTATUS.
      *    AND WCHQ-RET-NO  NOT =  '3'
      *    IF  WCHQ-TYPE = 'ABC' AND TRANS-DATE < WIN-DATE
      ***      AND (TRANS-DATE < WIN-DATE AND WIN-DATE <= NEXTB-DATE)
      *        MOVE 'A'  TO ASTATUS.
           IF (SSTATUS = 'S' AND PSTATUS = SPACE AND RSTATUS = SPACE)
      *    OR (RSTATUS = 'R' AND WCHQ-RET-NO = '3')
      *    OR (RSTATUS = 'R' AND WCHQ-RET-NO = '03')
           OR (SSTATUS = SPACE AND CSTATUS = SPACE
           AND PSTATUS = SPACE AND RSTATUS = SPACE)
               MOVE 'O'  TO OSTATUS.
           EXEC SQL
                CALL BCSCALF(:WCHQ-BR-RCV,
                             :AC-NO,
                             :WCHQ-BK,
                             :WCHQ-BR,
                             :WCHQ-AMT,
                             :WCHQ-TYPE,
                             :WAC-BR,
                             :SP-APP-NAME,
                             :SP-STATUS-FLAG,
                             :SP-STATUS-NUMBER,
                             :SP-SQLSTATE,
                             :SP-SQLCODE,
                             :SP-NB-FLAG,
                             :SP-NAME,
                             :WCHQ-COM,
                             :WK-ZONE-CHARGE,
                             :WK-AC-GROUP,
                             :WK-TYPE-CAL,
                             :WK-CHARGE-TYPE,
                             :WK-COM-TYPE,
                             :WK-CHRG-TYPE,
                             :WK-TRAN-BASE,
                             :WK-NORM-BASE,
                             :WK-NORM-BASE-MIN,
                             :WK-NORM-BASE-MAX,
                             :WK-VAL-BASE-AMT-RATE,
                             :WK-VAL-BASE-AMT-MIN,
                             :WK-VAL-BASE-AMT-MAX,
                             :WK-VAL-BASE-TIER-NO)
           END-EXEC.
           IF  TYPE-BCDAY = 'BC' AND ASTATUS = 'A' AND
              WCHQ-TYPE = 'ABC'
               MOVE  WIN-DATE                TO  WDATE
               MOVE  W1-YY2                  TO  W2-YY
               MOVE  W1-MM                   TO  W2-MM
               MOVE  W1-DD                   TO  W2-DD
               MOVE '/'                      TO  W2-F1
               MOVE '/'                      TO  W2-F2
               MOVE  W2-DATE                 TO  DIN-DATE
               PERFORM  0222-EDATE      THRU  0222-EXIT
               PERFORM  0444-COM        THRU  0444-EXIT
      *        MOVE  WABC-BEF-EDATE          TO  DBC-EDATE
      *        MOVE  WABC-BEF-DDATE          TO  DBC-DDATE
      *        MOVE  WABC-COM                TO  DABC-COM
           ELSE
           IF  TYPE-BCDAY = 'CL' AND
              ASTATUS = 'A' AND WCHQ-TYPE = 'ABC'
               MOVE  WIN-DATE                TO  WDATE
               MOVE  W1-YY2                  TO  W2-YY
               MOVE  W1-MM                   TO  W2-MM
               MOVE  W1-DD                   TO  W2-DD
               MOVE '/'                      TO  W2-F1
               MOVE '/'                      TO  W2-F2
               MOVE  W2-DATE                 TO  DIN-DATE
               PERFORM  0222-EDATE      THRU  0222-EXIT
               PERFORM  0444-COM        THRU  0444-EXIT
      *        MOVE  WABC-BEF-EDATE          TO  DBC-EDATE
      *        MOVE  WABC-BEF-DDATE          TO  DBC-DDATE
      *        MOVE  WABC-COM                TO  DABC-COM
           ELSE
               MOVE  SPACE                   TO  DIN-DATE
               MOVE  SPACE                   TO  DBC-EDATE
               MOVE  SPACE                   TO  DBC-DDATE
               MOVE  0.000                   TO  DABC-COM.
      *    IF  BSETBR    NOT =  BRSET-KEY
      *        MOVE SPACES      TO  SETBRNA
      *        PERFORM  1180-READ-CHQBRSET          THRU  1180-EXIT.
      *    MOVE  SETBRNA    TO  BSETBRNA.
      *    MOVE  0            TO  ACNO-KEY BRSET-KEY.
      *    MOVE  BACNO        TO  ACNO-KEY.
      *    MOVE  BSETBR       TO  BRSET-KEY.
      *    ADD   1            TO  SER-LINE.
      *    MOVE  SER-LINE     TO  SERIAL-NO.
      *    IF (BCHQSTAT  = SPACE) OR
      *       (BCHQSTAT  = ' ')
      *       MOVE  '0'  TO   BCHQSTAT.
       4400-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       4500-CHECK-CODE.
      *-------------------------------------------------------------*
           IF  WCHQ-COR  = '1'
               MOVE CODE1  TO DCHQ-COR.
           IF  WCHQ-COR  = '2'
               MOVE CODE2  TO DCHQ-COR.
           IF  WCHQ-COR  = '3'
               MOVE CODE3  TO DCHQ-COR.
           IF  WCHQ-COR  = '4'
               MOVE CODE4  TO DCHQ-COR.
           IF  WCHQ-COR  = '5'
               MOVE CODE5  TO DCHQ-COR.
           IF  WCHQ-COR  = '6'
               MOVE CODE6  TO DCHQ-COR.
           IF  WCHQ-COR  = '7'
               MOVE CODE7  TO DCHQ-COR.
           IF  WCHQ-COR  = '8'
               MOVE CODE8  TO DCHQ-COR.
           IF  WCHQ-COR  = '9'
               MOVE CODE9  TO DCHQ-COR.
           IF  WCHQ-COR  = SPACE
               MOVE SPACE  TO DCHQ-COR.
       4500-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       0222-EDATE.
      *-------------------------------------------------------------*
           IF  WAC-GROUP = 'Y'
               IF  WCHQ-BK  =  '014'
                   IF  WK-ZONE-CHARGE = 'S'
                       MOVE WGABC-IC-EDATE TO DBC-EDATE
                       MOVE WGABC-IC-DDATE TO DBC-DDATE
                   ELSE
                       MOVE WGABC-ICBC-EDATE TO DBC-EDATE
                       MOVE WGABC-ICBC-DDATE TO DBC-DDATE
               ELSE
                   IF  WK-ZONE-CHARGE = 'S'
                       MOVE WGABC-CL-EDATE TO DBC-EDATE
                       MOVE WGABC-CL-DDATE TO DBC-DDATE
                   ELSE
                       MOVE WGABC-OCBC-EDATE TO DBC-EDATE
                       MOVE WGABC-OCBC-DDATE TO DBC-DDATE
           ELSE
               IF  WCHQ-BK  =  '014'
                   IF  WK-ZONE-CHARGE = 'S'
                       MOVE WAABC-IC-EDATE TO DBC-EDATE
                       MOVE WAABC-IC-DDATE TO DBC-DDATE
                   ELSE
                       MOVE WAABC-ICBC-EDATE TO DBC-EDATE
                       MOVE WAABC-ICBC-DDATE TO DBC-DDATE
               ELSE
                   IF  WK-ZONE-CHARGE = 'S'
                       MOVE WAABC-CL-EDATE TO DBC-EDATE
                       MOVE WAABC-CL-DDATE TO DBC-DDATE
                   ELSE
                       MOVE WAABC-OCBC-EDATE TO DBC-EDATE
                       MOVE WAABC-OCBC-DDATE TO DBC-DDATE.

       0222-EXIT.
            EXIT.
      *-------------------------------------------------------------*
       0444-COM.
      *-------------------------------------------------------------*
           IF  WK-CHRG-TYPE  =  'T1'  THEN
               MOVE  0             TO  DABC-COM
           ELSE
               IF  WK-CHRG-TYPE  =  'V1'
                   MOVE  WK-NORM-BASE  TO  DABC-COM
               ELSE
                   IF  WK-CHRG-TYPE  =  'V2'  OR
                       WK-CHRG-TYPE  =  'V3'
                       MOVE  WK-VAL-BASE-AMT-RATE   TO  DABC-COM
                   ELSE
                       MOVE  0                      TO  DABC-COM.
       0444-EXIT.
            EXIT.
      *-------------------------------------------------------------*
       5000-MOVE-DETAIL2.
      *-------------------------------------------------------------*
           IF SSTATUS = 'S' AND  PSTATUS = 'P'
              MOVE 'S'         TO  DSTATUS
              MOVE 'N'         TO  DDISPLAY
              MOVE SPACE       TO  DCHQ-RET-NO
              MOVE 0           TO  DCHQ-RET-TIME
              PERFORM  5100-STATUS   THRU 5100-EXIT
           ELSE
              IF SSTATUS = 'S' AND  RSTATUS = 'R'
                 MOVE 'S'         TO  DSTATUS
                 MOVE 'N'         TO  DDISPLAY
                 MOVE SPACE       TO  DCHQ-RET-NO
                 MOVE 0           TO  DCHQ-RET-TIME
                 PERFORM  5100-STATUS   THRU 5100-EXIT
              ELSE
                 IF SSTATUS = 'S'
                    MOVE 'S'         TO  DSTATUS
                    MOVE 'Y'         TO  DDISPLAY
                    MOVE SPACE       TO  DCHQ-RET-NO
                    MOVE 0           TO  DCHQ-RET-TIME
                    PERFORM  5100-STATUS   THRU 5100-EXIT.
           IF CSTATUS = 'C'
              MOVE 'C'         TO  DSTATUS
              MOVE SPACE       TO  DCHQ-RET-NO
              MOVE 0           TO  DCHQ-RET-TIME
              PERFORM  5100-STATUS           THRU 5100-EXIT.
           IF PSTATUS = 'P'
              MOVE 'P'         TO  DSTATUS
              MOVE 'Y'         TO  DDISPLAY
              MOVE SPACE       TO  DCHQ-RET-NO
              MOVE 0           TO  DCHQ-RET-TIME
              PERFORM  5100-STATUS           THRU 5100-EXIT.
           IF ASTATUS = 'A'
      *       IF WCHQ-RET-NO = '14' OR WCHQ-RET-NO = '99'
      *             MOVE WCHQ-RET-NO      TO  DCHQ-RET-NO
      *          ELSE
                    MOVE 'A'              TO  DSTATUS
                    MOVE 'N'              TO  DDISPLAY
                    MOVE WCHQ-RET-NO      TO  DCHQ-RET-NO
                    MOVE WCHQ-RET-TIME    TO  DCHQ-RET-TIME
                    PERFORM  5200-DATA-ABC         THRU 5200-EXIT.
           IF RSTATUS = 'R'
              MOVE 'R'             TO  DSTATUS
              MOVE 'Y'             TO  DDISPLAY
              MOVE WCHQ-RET-NO     TO  DCHQ-RET-NO
              MOVE WCHQ-RET-TIME   TO  DCHQ-RET-TIME
              PERFORM  5100-STATUS           THRU 5100-EXIT.
      *    IF SSTATUS = 'S' AND OSTATUS = 'O' AND
      *      (RSTATUS = 'R' AND WCHQ-RET-NO = '3')  AND
      *      (RSTATUS = 'R' AND WCHQ-RET-NO = '03')
           IF SSTATUS = 'S' AND OSTATUS = 'O'
              MOVE 'O'              TO  DSTATUS
              MOVE 'N'              TO  DDISPLAY
              MOVE WCHQ-RET-NO      TO  DCHQ-RET-NO
              MOVE WCHQ-RET-TIME    TO  DCHQ-RET-TIME
              PERFORM  5100-STATUS           THRU 5100-EXIT
           ELSE
      *    IF OSTATUS = 'O' AND
      *      (RSTATUS = 'R' AND WCHQ-RET-NO = '3')
      *       MOVE 'O'              TO  DSTATUS
      *       MOVE 'Y'              TO  DDISPLAY
      *       MOVE WCHQ-RET-NO      TO  DCHQ-RET-NO
      *       MOVE WCHQ-RET-TIME    TO  DCHQ-RET-TIME
      *       PERFORM  5100-STATUS           THRU 5100-EXIT
      *    ELSE
              IF OSTATUS = 'O'
                 MOVE 'O'              TO  DSTATUS
                 MOVE 'N'              TO  DDISPLAY
                 MOVE WCHQ-RET-NO      TO  DCHQ-RET-NO
                 MOVE WCHQ-RET-TIME    TO  DCHQ-RET-TIME
                 PERFORM  5100-STATUS           THRU 5100-EXIT.
       5000-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       5100-STATUS.
      *-------------------------------------------------------------*
      *    IF (WCHQ-RET-NO = '3' OR WCHQ-RET-NO = '14' OR
      *                             WCHQ-RET-NO = '99')
      *        AND DSTATUS = 'S'
      *            MOVE WBEF-COM TO TMPCOM
      *    ELSE
      *        MOVE WCHQ-COM TO TMPCOM.

           ADD  1                           TO WK-KREC.
           MOVE WK-KREC                     TO DSERIAL-NO.

           EXEC  CICS  WRITE  DATASET('BCBCMDTL')
                              RIDFLD(DET-KEY)
                              LENGTH(WK-RECL)
                              FROM(DET-REC)
           END-EXEC.

      *    ADD  1  TO   CN-DATA.
       5100-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       5200-DATA-ABC.
      *-------------------------------------------------------------*
      *    IF (WCHQ-RET-NO = '3' OR WCHQ-RET-NO = '14' OR
      *                             WCHQ-RET-NO = '99')
      *       AND DSTATUS = 'S'
      *           MOVE WBEF-COM TO TMPCOM
      *    ELSE
      *       MOVE WCHQ-COM TO TMPCOM.

           ADD  1                           TO WK-KREC.
           MOVE WK-KREC                     TO DSERIAL-NO.

           EXEC  CICS  WRITE  DATASET('BCBCMDTL')
                              RIDFLD(DET-KEY)
                              LENGTH(WK-RECL)
                              FROM(DET-REC)
           END-EXEC.

      *    ADD  1  TO   CN-DATA.
       5200-EXIT.                                                       ********
            EXIT.
      *-------------------------------------------------------------*
       RTN-CLOSE.                                                       05910000
      *-------------------------------------------------------------*
           MOVE ZEROES                      TO DFHCOMMAREA.
           MOVE OUTREC                      TO DFHCOMMAREA.
           EXEC CICS RETURN END-EXEC.                                   05930000
           GOBACK.
                                                                        05960000
      *-------------------------------------------------------------*
       720-NO-SYSID.                                                    09420000
      *-------------------------------------------------------------*
           MOVE 'BCS'                       TO APPL-NAME.
           MOVE 'E '                        TO STATUS-FLAG.
           MOVE '01000'                     TO STATUS-NUMBER.
           MOVE 'SYSID ERROR.'              TO STATUS-DESC.
           MOVE '00000'                     TO SQL-STATE.
           MOVE '0000000000'                TO SQL-CODE.
           MOVE  OUTREC                     TO DFHCOMMAREA.

           EXEC CICS RETURN END-EXEC.                                   09630000
           GOBACK.

       720-EXIT.                                                        09650000
           EXIT.                                                        09660000
      *-------------------------------------------------------------*
       740-TERM-ERR.                                                    09670000
      *-------------------------------------------------------------*
           MOVE 'BCS'                       TO APPL-NAME.
           MOVE 'E '                        TO STATUS-FLAG.
           MOVE '02000'                     TO STATUS-NUMBER.
           MOVE 'TERM ERROR.'               TO STATUS-DESC.
           MOVE '00000'                     TO SQL-STATE.
           MOVE '0000000000'                TO SQL-CODE.
           MOVE OUTREC                      TO DFHCOMMAREA.

           EXEC CICS RETURN END-EXEC.                                   09630000
           GOBACK.

       740-EXIT.                                                        09900000
           EXIT.                                                        09910000
      *-------------------------------------------------------------*
       7777-ERROR.                                                      09920000
      *-------------------------------------------------------------*
           MOVE 'BCS'                       TO APPL-NAME.
           MOVE 'E '                        TO STATUS-FLAG.
           MOVE '03000'                     TO STATUS-NUMBER.
           MOVE 'INV REQ.'                  TO STATUS-DESC.
           MOVE '00000'                     TO SQL-STATE.
           MOVE '0000000000'                TO SQL-CODE.
           MOVE  OUTREC                     TO DFHCOMMAREA.

           EXEC CICS RETURN END-EXEC.                                   09630000
           GOBACK.

       7777-EXIT.                                                       09980000
            EXIT.
      *-------------------------------------------------------------*
       8888-ERROR.                                                      09920000
      *-------------------------------------------------------------*
           MOVE 'BCS'                       TO APPL-NAME.
           MOVE 'E '                        TO STATUS-FLAG.
           MOVE '99999'                     TO STATUS-NUMBER.
           MOVE 'CICS ERROR.'               TO STATUS-DESC.
           MOVE '00000'                     TO SQL-STATE.
           MOVE '0000000000'                TO SQL-CODE.
           MOVE  OUTREC                     TO DFHCOMMAREA.

           EXEC CICS RETURN END-EXEC.                                   09630000
           GOBACK.

       8888-EXIT.                                                       09980000
            EXIT.
      *-------------------------------------------------------------*
       9000-TERMINATE-RTN.                                              09420000
      *-------------------------------------------------------------*
           PERFORM 9010-CLOSE-CURSOR        THRU 9010-EXIT.

           IF SQLCODE  NOT = 0                                          09290000
              MOVE 'BCS'                     TO APPL-NAME
              MOVE 'E '                      TO STATUS-FLAG
              MOVE '00400'                   TO STATUS-NUMBER
              MOVE 'CLOSE SQL ERROR.'        TO STATUS-DESC
              MOVE '00000'                   TO SQL-STATE
              MOVE '0000000000'              TO SQL-CODE.

      * KOY : FOR CONNECT TO BCM
           MOVE ZEROES TO DFHCOMMAREA.
           MOVE OUTREC TO DFHCOMMAREA.

           EXEC CICS RETURN END-EXEC.                                   09630000
           GOBACK.

       9000-EXIT.                                                       09980000
            EXIT.
      *-------------------------------------------------------------*
       9010-CLOSE-CURSOR.                                               09420000
      *-------------------------------------------------------------*
           IF TRAN-ID = '0001' THEN
              EXEC  SQL  CLOSE  SUMMARY  END-EXEC.                      09600000

           IF TRAN-ID = '0002' AND STATUS-CHQ = '1' THEN
              EXEC  SQL  CLOSE  DETSETUP  END-EXEC.                     09600000

           IF TRAN-ID = '0002' AND STATUS-CHQ = '2' THEN
              EXEC  SQL  CLOSE  DETPAID  END-EXEC.                      09600000

           IF TRAN-ID = '0002' AND STATUS-CHQ = '3' THEN
              EXEC  SQL  CLOSE  DETRETURN  END-EXEC.                    09600000

           IF TRAN-ID = '0002' AND STATUS-CHQ = '4' THEN
              EXEC  SQL  CLOSE  DETALLTRN  END-EXEC.                    09600000

           IF TRAN-ID = '0002' AND STATUS-CHQ = '5' THEN
              EXEC  SQL  CLOSE  DETALLDWN  END-EXEC.                    09600000

       9010-EXIT.                                                       09980000
            EXIT.
      *-------------------------------------------------------------*
